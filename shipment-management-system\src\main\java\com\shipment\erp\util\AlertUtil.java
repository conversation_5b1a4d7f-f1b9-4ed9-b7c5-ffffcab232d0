package com.shipment.erp.util;

import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.scene.control.TextInputDialog;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Optional;

/**
 * أدوات الرسائل والتنبيهات
 * Alert and Message Utilities
 * 
 * يوفر طرق سهلة لعرض الرسائل والتنبيهات مع دعم اللغة العربية
 * Provides easy methods for displaying messages and alerts with Arabic support
 * 
 * <AUTHOR> إدارة الشحنات
 * @version 1.0.0
 */
public class AlertUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(AlertUtil.class);
    
    // منع إنشاء مثيل من الفئة
    // Prevent instantiation
    private AlertUtil() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * عرض رسالة معلومات
     * Show information message
     */
    public static void showInfo(String title, String message) {
        showAlert(Alert.AlertType.INFORMATION, title, message);
        logger.info("عرض رسالة معلومات: {}", title);
    }
    
    /**
     * عرض رسالة تحذير
     * Show warning message
     */
    public static void showWarning(String title, String message) {
        showAlert(Alert.AlertType.WARNING, title, message);
        logger.warn("عرض رسالة تحذير: {}", title);
    }
    
    /**
     * عرض رسالة خطأ
     * Show error message
     */
    public static void showError(String title, String message) {
        showAlert(Alert.AlertType.ERROR, title, message);
        logger.error("عرض رسالة خطأ: {}", title);
    }
    
    /**
     * عرض رسالة تأكيد
     * Show confirmation dialog
     */
    public static boolean showConfirmation(String title, String message) {
        Alert alert = createAlert(Alert.AlertType.CONFIRMATION, title, message);
        
        Optional<ButtonType> result = alert.showAndWait();
        boolean confirmed = result.isPresent() && result.get() == ButtonType.OK;
        
        logger.info("عرض رسالة تأكيد: {} - النتيجة: {}", title, confirmed ? "موافق" : "إلغاء");
        return confirmed;
    }
    
    /**
     * عرض رسالة تأكيد مخصصة
     * Show custom confirmation dialog
     */
    public static Optional<ButtonType> showConfirmation(String title, String message, 
                                                       ButtonType... buttonTypes) {
        Alert alert = createAlert(Alert.AlertType.CONFIRMATION, title, message);
        alert.getButtonTypes().setAll(buttonTypes);
        
        Optional<ButtonType> result = alert.showAndWait();
        logger.info("عرض رسالة تأكيد مخصصة: {}", title);
        return result;
    }
    
    /**
     * عرض مربع حوار لإدخال النص
     * Show text input dialog
     */
    public static Optional<String> showTextInput(String title, String headerText, String promptText) {
        TextInputDialog dialog = new TextInputDialog();
        dialog.setTitle(title);
        dialog.setHeaderText(headerText);
        dialog.setContentText(promptText);
        
        // تطبيق الأنماط العربية
        // Apply Arabic styles
        applyArabicStyles(dialog.getDialogPane().getScene().getWindow());
        
        Optional<String> result = dialog.showAndWait();
        logger.info("عرض مربع حوار إدخال النص: {}", title);
        return result;
    }
    
    /**
     * عرض مربع حوار لإدخال النص مع قيمة افتراضية
     * Show text input dialog with default value
     */
    public static Optional<String> showTextInput(String title, String headerText, 
                                               String promptText, String defaultValue) {
        TextInputDialog dialog = new TextInputDialog(defaultValue);
        dialog.setTitle(title);
        dialog.setHeaderText(headerText);
        dialog.setContentText(promptText);
        
        // تطبيق الأنماط العربية
        // Apply Arabic styles
        applyArabicStyles(dialog.getDialogPane().getScene().getWindow());
        
        Optional<String> result = dialog.showAndWait();
        logger.info("عرض مربع حوار إدخال النص مع قيمة افتراضية: {}", title);
        return result;
    }
    
    /**
     * عرض رسالة نجاح
     * Show success message
     */
    public static void showSuccess(String title, String message) {
        Alert alert = createAlert(Alert.AlertType.INFORMATION, title, message);
        alert.getDialogPane().getStyleClass().add("success-alert");
        alert.showAndWait();
        logger.info("عرض رسالة نجاح: {}", title);
    }
    
    /**
     * عرض رسالة مع تفاصيل إضافية
     * Show message with additional details
     */
    public static void showDetailedError(String title, String message, String details) {
        Alert alert = createAlert(Alert.AlertType.ERROR, title, message);
        alert.getDialogPane().setExpandableContent(new javafx.scene.control.TextArea(details));
        alert.showAndWait();
        logger.error("عرض رسالة خطأ مفصلة: {}", title);
    }
    
    /**
     * إنشاء تنبيه أساسي
     * Create basic alert
     */
    private static Alert createAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        
        // تطبيق الأنماط العربية
        // Apply Arabic styles
        applyArabicStyles(alert.getDialogPane().getScene().getWindow());
        
        return alert;
    }
    
    /**
     * عرض تنبيه أساسي
     * Show basic alert
     */
    private static void showAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = createAlert(alertType, title, message);
        alert.showAndWait();
    }
    
    /**
     * تطبيق الأنماط العربية على النافذة
     * Apply Arabic styles to window
     */
    private static void applyArabicStyles(javafx.stage.Window window) {
        if (window instanceof Stage) {
            Stage stage = (Stage) window;
            
            // تطبيق أنماط CSS للعربية
            // Apply CSS styles for Arabic
            try {
                stage.getScene().getStylesheets().add(
                    AlertUtil.class.getResource("/css/rtl.css").toExternalForm()
                );
                stage.getScene().getStylesheets().add(
                    AlertUtil.class.getResource("/css/alerts.css").toExternalForm()
                );
            } catch (Exception e) {
                logger.warn("تعذر تحميل أنماط CSS للتنبيهات", e);
            }
            
            // تعيين اتجاه النص من اليمين لليسار
            // Set right-to-left text direction
            stage.getScene().setNodeOrientation(javafx.geometry.NodeOrientation.RIGHT_TO_LEFT);
        }
    }
    
    /**
     * عرض رسالة تحميل
     * Show loading message
     */
    public static Alert showLoading(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.NONE);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        
        // إزالة الأزرار
        // Remove buttons
        alert.getButtonTypes().clear();
        
        // تطبيق الأنماط العربية
        // Apply Arabic styles
        applyArabicStyles(alert.getDialogPane().getScene().getWindow());
        
        alert.show();
        logger.info("عرض رسالة تحميل: {}", title);
        return alert;
    }
    
    /**
     * إغلاق رسالة التحميل
     * Close loading message
     */
    public static void closeLoading(Alert loadingAlert) {
        if (loadingAlert != null && loadingAlert.isShowing()) {
            loadingAlert.close();
            logger.info("إغلاق رسالة التحميل");
        }
    }
    
    /**
     * عرض رسالة مع مؤقت
     * Show message with timer
     */
    public static void showTimedMessage(String title, String message, int seconds) {
        Alert alert = createAlert(Alert.AlertType.INFORMATION, title, message);
        
        // إنشاء مؤقت لإغلاق الرسالة تلقائياً
        // Create timer to auto-close the message
        javafx.animation.Timeline timeline = new javafx.animation.Timeline(
            new javafx.animation.KeyFrame(
                javafx.util.Duration.seconds(seconds),
                e -> alert.close()
            )
        );
        
        timeline.play();
        alert.showAndWait();
        logger.info("عرض رسالة مؤقتة: {} لمدة {} ثانية", title, seconds);
    }
    
    /**
     * عرض رسالة تقدم العمل
     * Show progress message
     */
    public static Alert showProgress(String title, String message) {
        Alert alert = new Alert(Alert.AlertType.NONE);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        
        // إضافة شريط التقدم
        // Add progress bar
        javafx.scene.control.ProgressBar progressBar = new javafx.scene.control.ProgressBar();
        progressBar.setProgress(-1); // Indeterminate progress
        
        javafx.scene.layout.VBox vbox = new javafx.scene.layout.VBox(10);
        vbox.getChildren().addAll(
            new javafx.scene.control.Label(message),
            progressBar
        );
        
        alert.getDialogPane().setContent(vbox);
        alert.getButtonTypes().clear();
        
        // تطبيق الأنماط العربية
        // Apply Arabic styles
        applyArabicStyles(alert.getDialogPane().getScene().getWindow());
        
        alert.show();
        logger.info("عرض رسالة تقدم العمل: {}", title);
        return alert;
    }
}
