/* 
 * أنماط التنبيهات والرسائل لنظام إدارة الشحنات
 * Alert and Message Styles for Shipment Management System
 * 
 * يوفر أنماط متقدمة للتنبيهات والرسائل مع دعم RTL
 * Provides advanced styles for alerts and messages with RTL support
 */

/* ===== التنبيهات العامة ===== */
/* ===== General Alerts ===== */
.alert {
    -fx-background-color: white;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 10, 0, 0, 3);
    -fx-padding: 0;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
}

.alert .header-panel {
    -fx-background-radius: 8px 8px 0 0;
    -fx-padding: 16px 20px;
    -fx-border-width: 0 0 1px 0;
    -fx-border-color: #e9ecef;
}

.alert .header-panel .label {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

.alert .content {
    -fx-padding: 20px;
    -fx-font-size: 13px;
    -fx-text-alignment: right;
    -fx-alignment: top-right;
}

.alert .content .label {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
    -fx-wrap-text: true;
}

.alert .button-bar {
    -fx-padding: 12px 20px 20px 20px;
    -fx-alignment: center-left;
    -fx-spacing: 10px;
}

/* ===== تنبيهات المعلومات ===== */
/* ===== Information Alerts ===== */
.alert.information {
    -fx-border-color: #17a2b8;
    -fx-border-width: 2px;
}

.alert.information .header-panel {
    -fx-background-color: linear-gradient(to bottom, #d1ecf1, #bee5eb);
    -fx-border-color: #bee5eb;
}

.alert.information .header-panel .label {
    -fx-text-fill: #0c5460;
}

.alert.information .content {
    -fx-text-fill: #0c5460;
}

.alert.information .graphic {
    -fx-fill: #17a2b8;
    -fx-font-family: "FontAwesome";
    -fx-font-size: 24px;
}

/* ===== تنبيهات النجاح ===== */
/* ===== Success Alerts ===== */
.alert.confirmation, .success-alert {
    -fx-border-color: #28a745;
    -fx-border-width: 2px;
}

.alert.confirmation .header-panel, .success-alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #d4edda, #c3e6cb);
    -fx-border-color: #c3e6cb;
}

.alert.confirmation .header-panel .label, .success-alert .header-panel .label {
    -fx-text-fill: #155724;
}

.alert.confirmation .content, .success-alert .content {
    -fx-text-fill: #155724;
}

.alert.confirmation .graphic, .success-alert .graphic {
    -fx-fill: #28a745;
    -fx-font-family: "FontAwesome";
    -fx-font-size: 24px;
}

/* ===== تنبيهات التحذير ===== */
/* ===== Warning Alerts ===== */
.alert.warning, .warning-alert {
    -fx-border-color: #ffc107;
    -fx-border-width: 2px;
}

.alert.warning .header-panel, .warning-alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #fff3cd, #ffeaa7);
    -fx-border-color: #ffeaa7;
}

.alert.warning .header-panel .label, .warning-alert .header-panel .label {
    -fx-text-fill: #856404;
}

.alert.warning .content, .warning-alert .content {
    -fx-text-fill: #856404;
}

.alert.warning .graphic, .warning-alert .graphic {
    -fx-fill: #ffc107;
    -fx-font-family: "FontAwesome";
    -fx-font-size: 24px;
}

/* ===== تنبيهات الخطأ ===== */
/* ===== Error Alerts ===== */
.alert.error, .error-alert {
    -fx-border-color: #dc3545;
    -fx-border-width: 2px;
}

.alert.error .header-panel, .error-alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #f8d7da, #f5c6cb);
    -fx-border-color: #f5c6cb;
}

.alert.error .header-panel .label, .error-alert .header-panel .label {
    -fx-text-fill: #721c24;
}

.alert.error .content, .error-alert .content {
    -fx-text-fill: #721c24;
}

.alert.error .graphic, .error-alert .graphic {
    -fx-fill: #dc3545;
    -fx-font-family: "FontAwesome";
    -fx-font-size: 24px;
}

/* ===== الرسائل المضمنة ===== */
/* ===== Inline Messages ===== */
.inline-alert {
    -fx-padding: 12px 16px;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-border-width: 1px;
    -fx-font-family: "Tahoma", "Arial Unicode MS", sans-serif;
    -fx-font-size: 12px;
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

.inline-alert.info {
    -fx-background-color: #d1ecf1;
    -fx-border-color: #bee5eb;
    -fx-text-fill: #0c5460;
}

.inline-alert.success {
    -fx-background-color: #d4edda;
    -fx-border-color: #c3e6cb;
    -fx-text-fill: #155724;
}

.inline-alert.warning {
    -fx-background-color: #fff3cd;
    -fx-border-color: #ffeaa7;
    -fx-text-fill: #856404;
}

.inline-alert.error {
    -fx-background-color: #f8d7da;
    -fx-border-color: #f5c6cb;
    -fx-text-fill: #721c24;
}

/* ===== رسائل التحميل ===== */
/* ===== Loading Messages ===== */
.loading-alert {
    -fx-background-color: white;
    -fx-border-color: #0078d4;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 12, 0, 0, 4);
}

.loading-alert .header-panel {
    -fx-background-color: linear-gradient(to bottom, #e3f2fd, #bbdefb);
    -fx-border-color: #bbdefb;
    -fx-background-radius: 8px 8px 0 0;
}

.loading-alert .header-panel .label {
    -fx-text-fill: #0d47a1;
    -fx-font-weight: bold;
}

.loading-alert .content {
    -fx-text-fill: #1565c0;
    -fx-alignment: center;
    -fx-text-alignment: center;
}

.loading-alert .progress-indicator {
    -fx-progress-color: #0078d4;
    -fx-pref-width: 40px;
    -fx-pref-height: 40px;
}

.loading-alert .progress-bar {
    -fx-accent: #0078d4;
    -fx-pref-width: 200px;
    -fx-pref-height: 8px;
}

/* ===== رسائل التقدم ===== */
/* ===== Progress Messages ===== */
.progress-alert {
    -fx-background-color: white;
    -fx-border-color: #17a2b8;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 10, 0, 0, 3);
}

.progress-alert .content {
    -fx-alignment: center;
    -fx-spacing: 15px;
    -fx-padding: 25px;
}

.progress-alert .progress-label {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #0c5460;
    -fx-text-alignment: center;
}

.progress-alert .progress-details {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-text-alignment: center;
}

/* ===== التنبيهات المخصصة ===== */
/* ===== Custom Alerts ===== */
.custom-alert {
    -fx-background-color: white;
    -fx-border-radius: 10px;
    -fx-background-radius: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.25), 15, 0, 0, 5);
    -fx-padding: 0;
}

.custom-alert .icon-container {
    -fx-alignment: center;
    -fx-padding: 20px 20px 10px 20px;
}

.custom-alert .icon {
    -fx-font-size: 48px;
    -fx-text-alignment: center;
}

.custom-alert .title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-alignment: center;
    -fx-padding: 0 20px 10px 20px;
}

.custom-alert .message {
    -fx-font-size: 13px;
    -fx-text-alignment: center;
    -fx-alignment: center;
    -fx-padding: 0 20px 20px 20px;
    -fx-wrap-text: true;
}

.custom-alert .button-container {
    -fx-alignment: center;
    -fx-spacing: 10px;
    -fx-padding: 0 20px 20px 20px;
}

/* ===== التنبيهات المنبثقة ===== */
/* ===== Popup Alerts ===== */
.popup-alert {
    -fx-background-color: rgba(0, 0, 0, 0.8);
    -fx-background-radius: 8px;
    -fx-padding: 15px 20px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 8, 0, 0, 2);
}

.popup-alert .label {
    -fx-text-fill: white;
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-alignment: center;
}

/* ===== التنبيهات التفاعلية ===== */
/* ===== Interactive Alerts ===== */
.interactive-alert {
    -fx-background-color: white;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 12, 0, 0, 4);
}

.interactive-alert .input-container {
    -fx-spacing: 10px;
    -fx-padding: 20px;
    -fx-alignment: center-right;
}

.interactive-alert .input-label {
    -fx-font-size: 13px;
    -fx-font-weight: bold;
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

.interactive-alert .input-field {
    -fx-pref-width: 250px;
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

/* ===== الرسائل المؤقتة ===== */
/* ===== Temporary Messages ===== */
.toast-message {
    -fx-background-color: rgba(52, 58, 64, 0.95);
    -fx-background-radius: 6px;
    -fx-padding: 12px 16px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.3), 6, 0, 0, 2);
    -fx-max-width: 300px;
}

.toast-message .label {
    -fx-text-fill: white;
    -fx-font-size: 12px;
    -fx-text-alignment: right;
    -fx-alignment: center-right;
    -fx-wrap-text: true;
}

.toast-message.success {
    -fx-background-color: rgba(40, 167, 69, 0.95);
}

.toast-message.error {
    -fx-background-color: rgba(220, 53, 69, 0.95);
}

.toast-message.warning {
    -fx-background-color: rgba(255, 193, 7, 0.95);
}

.toast-message.warning .label {
    -fx-text-fill: #212529;
}

.toast-message.info {
    -fx-background-color: rgba(23, 162, 184, 0.95);
}

/* ===== الرسائل التفصيلية ===== */
/* ===== Detailed Messages ===== */
.detailed-alert .expandable-content {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px 0 0 0;
    -fx-padding: 15px 20px;
}

.detailed-alert .details-text {
    -fx-font-family: "Consolas", "Courier New", monospace;
    -fx-font-size: 11px;
    -fx-text-fill: #495057;
    -fx-background-color: white;
    -fx-border-color: #ced4da;
    -fx-border-width: 1px;
    -fx-border-radius: 4px;
    -fx-background-radius: 4px;
    -fx-padding: 10px;
}

/* ===== الحركات والانتقالات ===== */
/* ===== Animations and Transitions ===== */
.alert.fade-in {
    -fx-opacity: 0;
}

.alert.fade-out {
    -fx-opacity: 1;
}

.alert.slide-in {
    -fx-translate-y: -50;
}

.alert.slide-out {
    -fx-translate-y: 0;
}

/* ===== الاستجابة للشاشات المختلفة ===== */
/* ===== Responsive Design ===== */
@media screen and (max-width: 600px) {
    .alert {
        -fx-max-width: 90%;
        -fx-pref-width: 90%;
    }
    
    .alert .content {
        -fx-font-size: 12px;
    }
    
    .alert .header-panel .label {
        -fx-font-size: 14px;
    }
}
