<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.image.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<!--
شاشة تسجيل الدخول لنظام إدارة الشحنات
Login Screen for Shipment Management System

تدعم اللغة العربية مع محاذاة RTL
Supports Arabic language with RTL alignment
-->

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
            fx:controller="com.shipment.erp.controller.LoginController"
            styleClass="login-container">
   
   <!-- الخلفية الرئيسية -->
   <!-- Main Background -->
   <center>
      <HBox alignment="CENTER" spacing="0" styleClass="login-main-container">
         
         <!-- الجانب الأيسر - معلومات النظام -->
         <!-- Left Side - System Information -->
         <VBox alignment="CENTER" prefWidth="400" styleClass="login-info-panel">
            <padding>
               <Insets bottom="40" left="40" right="40" top="40" />
            </padding>
            
            <!-- شعار النظام -->
            <!-- System Logo -->
            <ImageView fitHeight="120" fitWidth="120" preserveRatio="true" styleClass="login-logo">
               <image>
                  <Image url="@../images/logo.png" />
               </image>
            </ImageView>
            
            <!-- عنوان النظام -->
            <!-- System Title -->
            <Label text="%app.title" styleClass="login-title">
               <font>
                  <Font name="Tahoma Bold" size="24" />
               </font>
               <VBox.margin>
                  <Insets top="20" />
               </VBox.margin>
            </Label>
            
            <!-- وصف النظام -->
            <!-- System Description -->
            <Label text="%app.subtitle" styleClass="login-subtitle" textAlignment="CENTER" wrapText="true">
               <font>
                  <Font name="Tahoma" size="14" />
               </font>
               <VBox.margin>
                  <Insets top="10" />
               </VBox.margin>
            </Label>
            
            <!-- معلومات إضافية -->
            <!-- Additional Information -->
            <VBox alignment="CENTER" spacing="10" styleClass="login-features">
               <VBox.margin>
                  <Insets top="30" />
               </VBox.margin>
               
               <HBox alignment="CENTER_RIGHT" spacing="10">
                  <Label text="✓" styleClass="feature-icon" />
                  <Label text="إدارة شاملة للشحنات" styleClass="feature-text" />
               </HBox>
               
               <HBox alignment="CENTER_RIGHT" spacing="10">
                  <Label text="✓" styleClass="feature-icon" />
                  <Label text="تتبع الشحنات في الوقت الفعلي" styleClass="feature-text" />
               </HBox>
               
               <HBox alignment="CENTER_RIGHT" spacing="10">
                  <Label text="✓" styleClass="feature-icon" />
                  <Label text="إدارة العملاء والموردين" styleClass="feature-text" />
               </HBox>
               
               <HBox alignment="CENTER_RIGHT" spacing="10">
                  <Label text="✓" styleClass="feature-icon" />
                  <Label text="تقارير مفصلة ومتقدمة" styleClass="feature-text" />
               </HBox>
            </VBox>
         </VBox>
         
         <!-- الجانب الأيمن - نموذج تسجيل الدخول -->
         <!-- Right Side - Login Form -->
         <VBox alignment="CENTER" prefWidth="400" styleClass="login-form-panel">
            <padding>
               <Insets bottom="40" left="40" right="40" top="40" />
            </padding>
            
            <!-- عنوان تسجيل الدخول -->
            <!-- Login Title -->
            <Label text="%login.title" styleClass="login-form-title">
               <font>
                  <Font name="Tahoma Bold" size="20" />
               </font>
               <VBox.margin>
                  <Insets bottom="30" />
               </VBox.margin>
            </Label>
            
            <!-- نموذج تسجيل الدخول -->
            <!-- Login Form -->
            <VBox spacing="20" styleClass="login-form">
               
               <!-- حقل اسم المستخدم -->
               <!-- Username Field -->
               <VBox spacing="5">
                  <Label text="%login.username" styleClass="form-label" />
                  <TextField fx:id="usernameField" promptText="%login.username" 
                           styleClass="form-field" prefHeight="40" />
               </VBox>
               
               <!-- حقل كلمة المرور -->
               <!-- Password Field -->
               <VBox spacing="5">
                  <Label text="%login.password" styleClass="form-label" />
                  <PasswordField fx:id="passwordField" promptText="%login.password" 
                               styleClass="form-field" prefHeight="40" />
               </VBox>
               
               <!-- خيارات إضافية -->
               <!-- Additional Options -->
               <HBox alignment="CENTER_LEFT" spacing="10">
                  <CheckBox fx:id="rememberMeCheckBox" text="%login.remember_me" 
                           styleClass="remember-checkbox" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Hyperlink text="%login.forgot_password" styleClass="forgot-password-link" 
                           onAction="#handleForgotPassword" />
               </HBox>
               
               <!-- رسالة الخطأ -->
               <!-- Error Message -->
               <Label fx:id="errorLabel" styleClass="error-message" visible="false" 
                     wrapText="true" textAlignment="CENTER" />
               
               <!-- أزرار العمل -->
               <!-- Action Buttons -->
               <VBox spacing="15">
                  <VBox.margin>
                     <Insets top="10" />
                  </VBox.margin>
                  
                  <!-- زر تسجيل الدخول -->
                  <!-- Login Button -->
                  <Button fx:id="loginButton" text="%login.login" 
                         onAction="#handleLogin" styleClass="login-button" 
                         prefHeight="45" maxWidth="Infinity" defaultButton="true">
                     <font>
                        <Font name="Tahoma Bold" size="14" />
                     </font>
                  </Button>
                  
                  <!-- زر الإلغاء -->
                  <!-- Cancel Button -->
                  <Button fx:id="cancelButton" text="%button.cancel" 
                         onAction="#handleCancel" styleClass="cancel-button" 
                         prefHeight="40" maxWidth="Infinity" cancelButton="true">
                     <font>
                        <Font name="Tahoma" size="12" />
                     </font>
                  </Button>
               </VBox>
            </VBox>
            
            <!-- معلومات الإصدار -->
            <!-- Version Information -->
            <Label fx:id="versionLabel" text="الإصدار 1.0.0" styleClass="version-label">
               <VBox.margin>
                  <Insets top="30" />
               </VBox.margin>
               <font>
                  <Font name="Tahoma" size="10" />
               </font>
            </Label>
         </VBox>
      </HBox>
   </center>
   
   <!-- شريط الحالة -->
   <!-- Status Bar -->
   <bottom>
      <HBox alignment="CENTER_LEFT" spacing="10" styleClass="login-status-bar">
         <padding>
            <Insets bottom="10" left="20" right="20" top="10" />
         </padding>
         
         <!-- حالة الاتصال بقاعدة البيانات -->
         <!-- Database Connection Status -->
         <Label fx:id="dbStatusLabel" text="%status.database_connected" 
               styleClass="status-label" />
         
         <Region HBox.hgrow="ALWAYS" />
         
         <!-- معلومات حقوق الطبع -->
         <!-- Copyright Information -->
         <Label text="%app.copyright" styleClass="copyright-label">
            <font>
               <Font name="Tahoma" size="9" />
            </font>
         </Label>
      </HBox>
   </bottom>
</BorderPane>
