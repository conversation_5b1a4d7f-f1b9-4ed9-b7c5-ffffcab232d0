package com.shipment.erp.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.shipment.erp.config.DatabaseConfig;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;

/**
 * خدمة إدارة المستخدمين
 * User Management Service
 * 
 * توفر عمليات إدارة المستخدمين والتحقق من الهوية
 * Provides user management operations and authentication
 * 
 * <AUTHOR> إدارة الشحنات
 * @version 1.0.0
 */
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    // استعلامات SQL
    // SQL Queries
    private static final String AUTHENTICATE_USER_QUERY = 
        "SELECT user_id, username, full_name, email, role, is_active, " +
        "failed_login_attempts, locked_until FROM users " +
        "WHERE username = ? AND password_hash = ? AND is_active = 1";
    
    private static final String UPDATE_LOGIN_SUCCESS_QUERY = 
        "UPDATE users SET last_login = SYSDATE, failed_login_attempts = 0, " +
        "locked_until = NULL WHERE user_id = ?";
    
    private static final String UPDATE_LOGIN_FAILURE_QUERY = 
        "UPDATE users SET failed_login_attempts = failed_login_attempts + 1, " +
        "locked_until = CASE WHEN failed_login_attempts + 1 >= 3 THEN SYSDATE + INTERVAL '15' MINUTE ELSE NULL END " +
        "WHERE username = ?";
    
    private static final String CHECK_USER_LOCKED_QUERY = 
        "SELECT locked_until FROM users WHERE username = ? AND locked_until > SYSDATE";
    
    private static final String GET_USER_BY_USERNAME_QUERY = 
        "SELECT user_id, username, full_name, email, role, is_active, " +
        "failed_login_attempts, locked_until, last_login FROM users WHERE username = ?";
    
    /**
     * التحقق من صحة بيانات المستخدم
     * Authenticate user credentials
     * 
     * @param username اسم المستخدم
     * @param passwordHash كلمة المرور المشفرة
     * @return true إذا كانت البيانات صحيحة
     */
    public boolean authenticateUser(String username, String passwordHash) {
        logger.info("محاولة التحقق من المستخدم: {} - Authenticating user: {}", username, username);
        
        try {
            // التحقق من قفل الحساب
            // Check if account is locked
            if (isUserLocked(username)) {
                logger.warn("الحساب مقفل: {} - Account locked: {}", username, username);
                return false;
            }
            
            // التحقق من بيانات المستخدم
            // Verify user credentials
            try (Connection connection = DatabaseConfig.getInstance().getConnection();
                 PreparedStatement statement = connection.prepareStatement(AUTHENTICATE_USER_QUERY)) {
                
                statement.setString(1, username);
                statement.setString(2, passwordHash);
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        int userId = resultSet.getInt("user_id");
                        String fullName = resultSet.getString("full_name");
                        String role = resultSet.getString("role");
                        
                        logger.info("تم التحقق من المستخدم بنجاح: {} ({}) - دور: {} - User authenticated successfully: {} ({}) - Role: {}", 
                                  username, fullName, role, username, fullName, role);
                        
                        // تحديث معلومات تسجيل الدخول الناجح
                        // Update successful login information
                        updateLoginSuccess(userId);
                        
                        return true;
                    } else {
                        logger.warn("فشل التحقق من المستخدم: {} - Authentication failed for user: {}", username, username);
                        
                        // تحديث معلومات تسجيل الدخول الفاشل
                        // Update failed login information
                        updateLoginFailure(username);
                        
                        return false;
                    }
                }
            }
            
        } catch (SQLException e) {
            logger.error("خطأ في قاعدة البيانات أثناء التحقق من المستخدم: {} - Database error during authentication: {}", 
                        username, username, e);
            return false;
        } catch (Exception e) {
            logger.error("خطأ غير متوقع أثناء التحقق من المستخدم: {} - Unexpected error during authentication: {}", 
                        username, username, e);
            return false;
        }
    }
    
    /**
     * التحقق من قفل المستخدم
     * Check if user is locked
     * 
     * @param username اسم المستخدم
     * @return true إذا كان المستخدم مقفل
     */
    public boolean isUserLocked(String username) {
        try (Connection connection = DatabaseConfig.getInstance().getConnection();
             PreparedStatement statement = connection.prepareStatement(CHECK_USER_LOCKED_QUERY)) {
            
            statement.setString(1, username);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                boolean locked = resultSet.next();
                if (locked) {
                    logger.info("المستخدم مقفل: {} - User is locked: {}", username, username);
                }
                return locked;
            }
            
        } catch (SQLException e) {
            logger.error("خطأ في التحقق من قفل المستخدم: {} - Error checking user lock: {}", username, username, e);
            return false;
        }
    }
    
    /**
     * تحديث معلومات تسجيل الدخول الناجح
     * Update successful login information
     * 
     * @param userId معرف المستخدم
     */
    private void updateLoginSuccess(int userId) {
        try (Connection connection = DatabaseConfig.getInstance().getConnection();
             PreparedStatement statement = connection.prepareStatement(UPDATE_LOGIN_SUCCESS_QUERY)) {
            
            statement.setInt(1, userId);
            int rowsUpdated = statement.executeUpdate();
            
            if (rowsUpdated > 0) {
                logger.info("تم تحديث معلومات تسجيل الدخول الناجح للمستخدم: {} - Updated successful login for user: {}", 
                          userId, userId);
            }
            
        } catch (SQLException e) {
            logger.error("خطأ في تحديث معلومات تسجيل الدخول الناجح: {} - Error updating successful login: {}", 
                        userId, userId, e);
        }
    }
    
    /**
     * تحديث معلومات تسجيل الدخول الفاشل
     * Update failed login information
     * 
     * @param username اسم المستخدم
     */
    private void updateLoginFailure(String username) {
        try (Connection connection = DatabaseConfig.getInstance().getConnection();
             PreparedStatement statement = connection.prepareStatement(UPDATE_LOGIN_FAILURE_QUERY)) {
            
            statement.setString(1, username);
            int rowsUpdated = statement.executeUpdate();
            
            if (rowsUpdated > 0) {
                logger.info("تم تحديث معلومات تسجيل الدخول الفاشل للمستخدم: {} - Updated failed login for user: {}", 
                          username, username);
            }
            
        } catch (SQLException e) {
            logger.error("خطأ في تحديث معلومات تسجيل الدخول الفاشل: {} - Error updating failed login: {}", 
                        username, username, e);
        }
    }
    
    /**
     * الحصول على معلومات المستخدم
     * Get user information
     * 
     * @param username اسم المستخدم
     * @return معلومات المستخدم أو null إذا لم يوجد
     */
    public UserInfo getUserInfo(String username) {
        logger.debug("الحصول على معلومات المستخدم: {} - Getting user info: {}", username, username);
        
        try (Connection connection = DatabaseConfig.getInstance().getConnection();
             PreparedStatement statement = connection.prepareStatement(GET_USER_BY_USERNAME_QUERY)) {
            
            statement.setString(1, username);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    UserInfo userInfo = new UserInfo();
                    userInfo.setUserId(resultSet.getInt("user_id"));
                    userInfo.setUsername(resultSet.getString("username"));
                    userInfo.setFullName(resultSet.getString("full_name"));
                    userInfo.setEmail(resultSet.getString("email"));
                    userInfo.setRole(resultSet.getString("role"));
                    userInfo.setActive(resultSet.getBoolean("is_active"));
                    userInfo.setFailedAttempts(resultSet.getInt("failed_login_attempts"));
                    
                    // تحويل التاريخ
                    // Convert timestamp
                    java.sql.Timestamp lockedUntil = resultSet.getTimestamp("locked_until");
                    if (lockedUntil != null) {
                        userInfo.setLockedUntil(lockedUntil.toLocalDateTime());
                    }
                    
                    java.sql.Timestamp lastLogin = resultSet.getTimestamp("last_login");
                    if (lastLogin != null) {
                        userInfo.setLastLogin(lastLogin.toLocalDateTime());
                    }
                    
                    logger.debug("تم العثور على معلومات المستخدم: {} - Found user info: {}", username, username);
                    return userInfo;
                } else {
                    logger.debug("لم يتم العثور على المستخدم: {} - User not found: {}", username, username);
                    return null;
                }
            }
            
        } catch (SQLException e) {
            logger.error("خطأ في الحصول على معلومات المستخدم: {} - Error getting user info: {}", 
                        username, username, e);
            return null;
        }
    }
    
    /**
     * فئة معلومات المستخدم
     * User Information Class
     */
    public static class UserInfo {
        private int userId;
        private String username;
        private String fullName;
        private String email;
        private String role;
        private boolean active;
        private int failedAttempts;
        private LocalDateTime lockedUntil;
        private LocalDateTime lastLogin;
        
        // Getters and Setters
        public int getUserId() { return userId; }
        public void setUserId(int userId) { this.userId = userId; }
        
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getRole() { return role; }
        public void setRole(String role) { this.role = role; }
        
        public boolean isActive() { return active; }
        public void setActive(boolean active) { this.active = active; }
        
        public int getFailedAttempts() { return failedAttempts; }
        public void setFailedAttempts(int failedAttempts) { this.failedAttempts = failedAttempts; }
        
        public LocalDateTime getLockedUntil() { return lockedUntil; }
        public void setLockedUntil(LocalDateTime lockedUntil) { this.lockedUntil = lockedUntil; }
        
        public LocalDateTime getLastLogin() { return lastLogin; }
        public void setLastLogin(LocalDateTime lastLogin) { this.lastLogin = lastLogin; }
        
        @Override
        public String toString() {
            return String.format("UserInfo{userId=%d, username='%s', fullName='%s', role='%s', active=%s}", 
                               userId, username, fullName, role, active);
        }
    }
    
    /**
     * التحقق من صحة اتصال الخدمة
     * Validate service connection
     * 
     * @return true إذا كان الاتصال صحيح
     */
    public boolean isServiceAvailable() {
        try {
            return DatabaseConfig.getInstance().isConnectionValid();
        } catch (Exception e) {
            logger.error("خطأ في التحقق من توفر الخدمة - Error checking service availability", e);
            return false;
        }
    }
    
    /**
     * إغلاق الخدمة وتنظيف الموارد
     * Close service and cleanup resources
     */
    public void close() {
        logger.info("إغلاق خدمة المستخدمين - Closing user service");
        // تنظيف أي موارد إضافية إذا لزم الأمر
        // Cleanup any additional resources if needed
    }
}
