-- إنشاء مستخدم قاعدة البيانات لنظام إدارة الشحنات
-- Create database user for Shipment Management System

-- تعيين إعدادات الجلسة للعربية
-- Set session settings for Arabic
ALTER SESSION SET NLS_LANGUAGE='ARABIC';
ALTER SESSION SET NLS_TERRITORY='EGYPT';

-- إنشاء المستخدم
-- Create user
CREATE USER ship_erp IDENTIFIED BY ys123;

-- منح الصلاحيات الأساسية
-- Grant basic privileges
GRANT CONNECT TO ship_erp;
GRANT RESOURCE TO ship_erp;
GRANT CREATE SESSION TO ship_erp;
GRANT CREATE TABLE TO ship_erp;
GRANT CREATE VIEW TO ship_erp;
GRANT CREATE SEQUENCE TO ship_erp;
GRANT CREATE PROCEDURE TO ship_erp;
GRANT CREATE TRIGGER TO ship_erp;

-- منح مساحة تخزين غير محدودة
-- Grant unlimited tablespace
GRANT UNLIMITED TABLESPACE TO ship_erp;

-- منح صلاحيات إضافية للتطوير
-- Grant additional privileges for development
GRANT CREATE ANY INDEX TO ship_erp;
GRANT ALTER ANY TABLE TO ship_erp;
GRANT DROP ANY TABLE TO ship_erp;

-- تأكيد العمليات
-- Commit changes
COMMIT;

-- عرض رسالة نجاح
-- Display success message
SELECT 'تم إنشاء المستخدم ship_erp بنجاح - User ship_erp created successfully' AS STATUS FROM DUAL;

EXIT;
