# Java Logging Configuration for Shipment Management System
# إعدادات السجلات لنظام إدارة الشحنات

# Global logging level
.level = INFO

# Console handler
handlers = java.util.logging.ConsoleHandler

# Console handler configuration
java.util.logging.ConsoleHandler.level = INFO
java.util.logging.ConsoleHandler.formatter = java.util.logging.SimpleFormatter

# Oracle JDBC logging - reduce verbosity
oracle.level = WARNING
oracle.jdbc.level = WARNING
oracle.net.level = WARNING

# Application logging
com.shipment.erp.level = INFO

# Disable problematic Oracle regex patterns
oracle.jdbc.defaultNChar = false
oracle.jdbc.convertNcharLiterals = false
