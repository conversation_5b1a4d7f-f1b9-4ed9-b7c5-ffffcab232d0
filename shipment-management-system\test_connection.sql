-- اختبار الاتصال بقاعدة البيانات
-- Test database connection

-- تعيين إعدادات الجلسة
-- Set session settings
SET PAGESIZE 50;
SET LINESIZE 100;

-- عرض معلومات الاتصال
-- Display connection information
SELECT 
    'اختبار الاتصال بقاعدة البيانات - Database Connection Test' AS TEST_TITLE
FROM DUAL;

-- عرض معلومات المستخدم الحالي
-- Display current user information
SELECT 
    'المستخدم الحالي - Current User: ' || USER AS CURRENT_USER,
    'التاريخ والوقت - Date/Time: ' || TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS') AS CURRENT_TIME
FROM DUAL;

-- عرض معلومات قاعدة البيانات
-- Display database information
SELECT 
    'اسم قاعدة البيانات - Database Name: ' || NAME AS DB_NAME
FROM V$DATABASE;

-- اختبار إنشاء جدول بسيط
-- Test creating a simple table
CREATE TABLE test_connection (
    id NUMBER PRIMARY KEY,
    test_message NVARCHAR2(100),
    created_date DATE DEFAULT SYSDATE
);

-- إدراج بيانات اختبار
-- Insert test data
INSERT INTO test_connection (id, test_message) 
VALUES (1, 'اختبار الاتصال ناجح - Connection test successful');

-- التأكيد
-- Commit
COMMIT;

-- عرض البيانات المدرجة
-- Display inserted data
SELECT 
    id,
    test_message,
    TO_CHAR(created_date, 'YYYY-MM-DD HH24:MI:SS') AS created_date
FROM test_connection;

-- حذف جدول الاختبار
-- Drop test table
DROP TABLE test_connection;

-- عرض رسالة نجاح نهائية
-- Display final success message
SELECT 
    'تم اختبار الاتصال بنجاح! - Connection test completed successfully!' AS RESULT
FROM DUAL;

EXIT;
