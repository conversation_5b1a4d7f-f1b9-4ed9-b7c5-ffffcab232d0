-- إنشاء جداول نظام إدارة الشحنات
-- Create Shipment Management System Tables

-- جدول المستخدمين
-- Users Table
CREATE TABLE users (
    user_id NUMBER(10) PRIMARY KEY,
    username VARCHAR2(50) UNIQUE NOT NULL,
    password_hash VARCHAR2(256) NOT NULL,
    full_name NVARCHAR2(100) NOT NULL,
    email VARCHAR2(100),
    role VARCHAR2(20) DEFAULT 'USER' NOT NULL,
    is_active NUMBER(1) DEFAULT 1 NOT NULL,
    is_locked NUMBER(1) DEFAULT 0 NOT NULL,
    failed_login_attempts NUMBER(3) DEFAULT 0,
    locked_until DATE,
    created_date DATE DEFAULT SYSDATE NOT NULL,
    last_login DATE,
    last_login_attempt DATE,
    created_by NUMBER(10),
    updated_by NUMBER(10),
    updated_date DATE
);

-- إنشاء تسلسل للمستخدمين
-- Create sequence for users
CREATE SEQUENCE users_seq START WITH 1 INCREMENT BY 1;

-- إن<PERSON>اء مؤشر على اسم المستخدم
-- Create index on username
CREATE INDEX idx_users_username ON users(username);

-- جدول العملاء
-- Customers Table
CREATE TABLE customers (
    customer_id NUMBER(10) PRIMARY KEY,
    customer_code VARCHAR2(20) UNIQUE NOT NULL,
    customer_name NVARCHAR2(200) NOT NULL,
    customer_name_en VARCHAR2(200),
    contact_person NVARCHAR2(100),
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    email VARCHAR2(100),
    address NVARCHAR2(500),
    city NVARCHAR2(50),
    country NVARCHAR2(50),
    postal_code VARCHAR2(20),
    tax_number VARCHAR2(50),
    credit_limit NUMBER(15,2) DEFAULT 0,
    customer_type VARCHAR2(20) DEFAULT 'REGULAR',
    is_active NUMBER(1) DEFAULT 1 NOT NULL,
    created_date DATE DEFAULT SYSDATE NOT NULL,
    created_by NUMBER(10),
    updated_by NUMBER(10),
    updated_date DATE
);

-- إنشاء تسلسل للعملاء
-- Create sequence for customers
CREATE SEQUENCE customers_seq START WITH 1 INCREMENT BY 1;

-- جدول الموردين
-- Suppliers Table
CREATE TABLE suppliers (
    supplier_id NUMBER(10) PRIMARY KEY,
    supplier_code VARCHAR2(20) UNIQUE NOT NULL,
    supplier_name NVARCHAR2(200) NOT NULL,
    supplier_name_en VARCHAR2(200),
    contact_person NVARCHAR2(100),
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    email VARCHAR2(100),
    address NVARCHAR2(500),
    city NVARCHAR2(50),
    country NVARCHAR2(50),
    postal_code VARCHAR2(20),
    tax_number VARCHAR2(50),
    supplier_type VARCHAR2(20) DEFAULT 'REGULAR',
    is_active NUMBER(1) DEFAULT 1 NOT NULL,
    created_date DATE DEFAULT SYSDATE NOT NULL,
    created_by NUMBER(10),
    updated_by NUMBER(10),
    updated_date DATE
);

-- إنشاء تسلسل للموردين
-- Create sequence for suppliers
CREATE SEQUENCE suppliers_seq START WITH 1 INCREMENT BY 1;

-- جدول المنتجات
-- Products Table
CREATE TABLE products (
    product_id NUMBER(10) PRIMARY KEY,
    product_code VARCHAR2(50) UNIQUE NOT NULL,
    product_name NVARCHAR2(200) NOT NULL,
    product_name_en VARCHAR2(200),
    description NVARCHAR2(1000),
    category NVARCHAR2(100),
    unit_of_measure VARCHAR2(20),
    weight NUMBER(10,3),
    dimensions VARCHAR2(100),
    unit_price NUMBER(15,2),
    is_active NUMBER(1) DEFAULT 1 NOT NULL,
    created_date DATE DEFAULT SYSDATE NOT NULL,
    created_by NUMBER(10),
    updated_by NUMBER(10),
    updated_date DATE
);

-- إنشاء تسلسل للمنتجات
-- Create sequence for products
CREATE SEQUENCE products_seq START WITH 1 INCREMENT BY 1;

-- جدول الشحنات
-- Shipments Table
CREATE TABLE shipments (
    shipment_id NUMBER(10) PRIMARY KEY,
    shipment_number VARCHAR2(50) UNIQUE NOT NULL,
    customer_id NUMBER(10) NOT NULL,
    supplier_id NUMBER(10),
    shipment_date DATE DEFAULT SYSDATE NOT NULL,
    expected_delivery_date DATE,
    actual_delivery_date DATE,
    origin_address NVARCHAR2(500),
    destination_address NVARCHAR2(500),
    shipment_status VARCHAR2(20) DEFAULT 'PENDING',
    total_weight NUMBER(10,3),
    total_volume NUMBER(10,3),
    total_value NUMBER(15,2),
    shipping_cost NUMBER(15,2),
    insurance_cost NUMBER(15,2),
    notes NVARCHAR2(1000),
    created_date DATE DEFAULT SYSDATE NOT NULL,
    created_by NUMBER(10),
    updated_by NUMBER(10),
    updated_date DATE,
    CONSTRAINT fk_shipments_customer FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
    CONSTRAINT fk_shipments_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id)
);

-- إنشاء تسلسل للشحنات
-- Create sequence for shipments
CREATE SEQUENCE shipments_seq START WITH 1 INCREMENT BY 1;

-- جدول تفاصيل الشحنات
-- Shipment Details Table
CREATE TABLE shipment_details (
    detail_id NUMBER(10) PRIMARY KEY,
    shipment_id NUMBER(10) NOT NULL,
    product_id NUMBER(10) NOT NULL,
    quantity NUMBER(10,2) NOT NULL,
    unit_price NUMBER(15,2),
    total_price NUMBER(15,2),
    weight NUMBER(10,3),
    volume NUMBER(10,3),
    notes NVARCHAR2(500),
    CONSTRAINT fk_shipment_details_shipment FOREIGN KEY (shipment_id) REFERENCES shipments(shipment_id),
    CONSTRAINT fk_shipment_details_product FOREIGN KEY (product_id) REFERENCES products(product_id)
);

-- إنشاء تسلسل لتفاصيل الشحنات
-- Create sequence for shipment details
CREATE SEQUENCE shipment_details_seq START WITH 1 INCREMENT BY 1;

-- جدول المستندات
-- Documents Table
CREATE TABLE documents (
    document_id NUMBER(10) PRIMARY KEY,
    document_number VARCHAR2(50) UNIQUE NOT NULL,
    document_type VARCHAR2(50) NOT NULL,
    shipment_id NUMBER(10),
    customer_id NUMBER(10),
    supplier_id NUMBER(10),
    document_date DATE DEFAULT SYSDATE NOT NULL,
    file_path VARCHAR2(500),
    file_name VARCHAR2(200),
    file_size NUMBER(15),
    mime_type VARCHAR2(100),
    description NVARCHAR2(500),
    is_active NUMBER(1) DEFAULT 1 NOT NULL,
    created_date DATE DEFAULT SYSDATE NOT NULL,
    created_by NUMBER(10),
    updated_by NUMBER(10),
    updated_date DATE,
    CONSTRAINT fk_documents_shipment FOREIGN KEY (shipment_id) REFERENCES shipments(shipment_id),
    CONSTRAINT fk_documents_customer FOREIGN KEY (customer_id) REFERENCES customers(customer_id),
    CONSTRAINT fk_documents_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(supplier_id)
);

-- إنشاء تسلسل للمستندات
-- Create sequence for documents
CREATE SEQUENCE documents_seq START WITH 1 INCREMENT BY 1;

-- جدول إعدادات النظام
-- System Settings Table
CREATE TABLE system_settings (
    setting_id NUMBER(10) PRIMARY KEY,
    setting_key VARCHAR2(100) UNIQUE NOT NULL,
    setting_value NVARCHAR2(1000),
    setting_type VARCHAR2(20) DEFAULT 'STRING',
    description NVARCHAR2(500),
    is_active NUMBER(1) DEFAULT 1 NOT NULL,
    created_date DATE DEFAULT SYSDATE NOT NULL,
    created_by NUMBER(10),
    updated_by NUMBER(10),
    updated_date DATE
);

-- إنشاء تسلسل لإعدادات النظام
-- Create sequence for system settings
CREATE SEQUENCE system_settings_seq START WITH 1 INCREMENT BY 1;

-- إدراج المستخدم الافتراضي
-- Insert default user
INSERT INTO users (
    user_id,
    username,
    password_hash,
    full_name,
    email,
    role,
    is_active,
    is_locked,
    failed_login_attempts,
    created_date
) VALUES (
    1,
    'admin',
    'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- admin123 hashed with SHA-256
    'مدير النظام',
    '<EMAIL>',
    'ADMIN',
    1,
    0,
    0,
    SYSDATE
);

-- إدراج إعدادات النظام الافتراضية
-- Insert default system settings
INSERT INTO system_settings (setting_id, setting_key, setting_value, description) VALUES (1, 'COMPANY_NAME', 'شركة الشحنات المتقدمة', 'اسم الشركة');
INSERT INTO system_settings (setting_id, setting_key, setting_value, description) VALUES (2, 'COMPANY_NAME_EN', 'Advanced Shipping Company', 'اسم الشركة بالإنجليزية');
INSERT INTO system_settings (setting_id, setting_key, setting_value, description) VALUES (3, 'DEFAULT_LANGUAGE', 'AR', 'اللغة الافتراضية');
INSERT INTO system_settings (setting_id, setting_key, setting_value, description) VALUES (4, 'DEFAULT_CURRENCY', 'SAR', 'العملة الافتراضية');
INSERT INTO system_settings (setting_id, setting_key, setting_value, description) VALUES (5, 'DECIMAL_PLACES', '2', 'عدد الأرقام العشرية');

COMMIT;

-- عرض الجداول المُنشأة
-- Show created tables
SELECT table_name FROM user_tables ORDER BY table_name;
