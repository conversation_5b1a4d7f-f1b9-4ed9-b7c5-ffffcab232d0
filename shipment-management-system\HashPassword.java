import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class HashPassword {
    public static void main(String[] args) {
        String password = "admin123";
        String hashedPassword = hashPassword(password);
        System.out.println("Password: " + password);
        System.out.println("Hashed: " + hashedPassword);
    }
    
    private static String hashPassword(String password) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(password.getBytes("UTF-8"));
            StringBuilder hexString = new StringBuilder();
            
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("Error hashing password", e);
        }
    }
}
