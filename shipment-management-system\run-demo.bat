@echo off
REM تشغيل نظام إدارة الشحنات - نسخة تجريبية
REM Run Shipment Management System - Demo Version

echo ========================================
echo نظام إدارة الشحنات - نسخة تجريبية
echo Shipment Management System - Demo
echo ========================================

REM التحقق من وجود Java
echo التحقق من تثبيت Java...
echo Checking Java installation...

java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت أو غير متاح في PATH
    echo Error: Java is not installed or not available in PATH
    echo يرجى تثبيت Java 17 أو أحدث
    echo Please install Java 17 or newer
    pause
    exit /b 1
)

echo تم العثور على Java
echo Java found

REM التحقق من وجود Maven
echo التحقق من تثبيت Maven...
echo Checking Maven installation...

E:\java\apache-maven-3.9.11\bin\mvn.cmd -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Maven غير متاح
    echo Error: Maven not available
    echo يرجى التأكد من مسار Maven
    echo Please check Maven path
    pause
    exit /b 1
)

echo تم العثور على Maven
echo Maven found

REM تجميع المشروع
echo ========================================
echo تجميع المشروع...
echo Compiling project...
echo ========================================

E:\java\apache-maven-3.9.11\bin\mvn.cmd clean compile

if %errorlevel% neq 0 (
    echo خطأ في تجميع المشروع
    echo Error compiling project
    pause
    exit /b 1
)

echo تم تجميع المشروع بنجاح
echo Project compiled successfully

REM تشغيل النظام
echo ========================================
echo تشغيل نظام إدارة الشحنات...
echo Starting Shipment Management System...
echo ========================================
echo.
echo معلومات تسجيل الدخول:
echo Login credentials:
echo اسم المستخدم: admin
echo Username: admin
echo كلمة المرور: admin123
echo Password: admin123
echo.
echo ملاحظة: النظام يعمل في وضع تجريبي بدون قاعدة بيانات
echo Note: System running in demo mode without database
echo.

REM تشغيل التطبيق مع إعدادات JavaFX
E:\java\apache-maven-3.9.11\bin\mvn.cmd javafx:run -Djavafx.args="--demo-mode"

echo.
echo تم إغلاق النظام
echo System closed
pause
