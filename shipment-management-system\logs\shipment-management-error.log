2025-07-17 19:36:44 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:134)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 19:51:12 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:134)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 19:52:43 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:135)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 19:55:19 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:130)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 20:18:54 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:130)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 20:21:33 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:135)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 20:50:47 [main] ERROR c.shipment.erp.ShipmentManagementApp - خطأ فادح في التطبيق - Fatal application error
java.lang.RuntimeException: Exception in Application init method
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:888)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.lambda$launchApplication$2(LauncherImpl.java:196)
	at java.base/java.lang.Thread.run(Thread.java:842)
Caused by: java.lang.ExceptionInInitializerError: null
	at oracle.net.ns.NSProtocol.<init>(NSProtocol.java:249)
	at oracle.net.ns.NSProtocolNIO.<init>(NSProtocolNIO.java:147)
	at oracle.jdbc.driver.T4CConnection.logon(T4CConnection.java:911)
	at oracle.jdbc.driver.PhysicalConnection.connect(PhysicalConnection.java:1157)
	at oracle.jdbc.driver.T4CDriverExtension.getConnection(T4CDriverExtension.java:104)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:825)
	at oracle.jdbc.driver.OracleDriver.connect(OracleDriver.java:651)
	at com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:137)
	at com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:360)
	at com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:202)
	at com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:461)
	at com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:550)
	at com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:98)
	at com.zaxxer.hikari.HikariDataSource.<init>(HikariDataSource.java:80)
	at com.shipment.erp.config.DatabaseConfig.initialize(DatabaseConfig.java:154)
	at com.shipment.erp.ShipmentManagementApp.initializeDatabase(ShipmentManagementApp.java:142)
	at com.shipment.erp.ShipmentManagementApp.init(ShipmentManagementApp.java:66)
	at javafx.graphics@21.0.1/com.sun.javafx.application.LauncherImpl.launchApplication1(LauncherImpl.java:817)
	... 2 common frames omitted
Caused by: java.util.regex.PatternSyntaxException: Illegal repetition near index 11
[A-z0-9,_]{٨}
           ^
	at java.base/java.util.regex.Pattern.error(Pattern.java:2028)
	at java.base/java.util.regex.Pattern.closure(Pattern.java:3309)
	at java.base/java.util.regex.Pattern.sequence(Pattern.java:2214)
	at java.base/java.util.regex.Pattern.expr(Pattern.java:2069)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1783)
	at java.base/java.util.regex.Pattern.<init>(Pattern.java:1430)
	at java.base/java.util.regex.Pattern.compile(Pattern.java:1069)
	at oracle.net.ns.SessionAtts.<clinit>(SessionAtts.java:148)
	... 20 common frames omitted
2025-07-17 21:25:29 [JavaFX Application Thread] ERROR c.s.erp.controller.MainController - خطأ في فتح النافذة - Error opening window for: المتغيرات العامة للبرنامج
javafx.fxml.LoadException: FXCollections is not a valid type.
/E:/java/shipment-management-system/target/classes/fxml/system-variables.fxml:141

	at javafx.fxml@21.0.1/javafx.fxml.FXMLLoader.constructLoadException(FXMLLoader.java:2718)
	at javafx.fxml@21.0.1/javafx.fxml.FXMLLoader.createElement(FXMLLoader.java:2920)
	at javafx.fxml@21.0.1/javafx.fxml.FXMLLoader.processStartElement(FXMLLoader.java:2850)
	at javafx.fxml@21.0.1/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2649)
	at javafx.fxml@21.0.1/javafx.fxml.FXMLLoader.loadImpl(FXMLLoader.java:2563)
	at javafx.fxml@21.0.1/javafx.fxml.FXMLLoader.load(FXMLLoader.java:2531)
	at com.shipment.erp.controller.MainController.openModalWindow(MainController.java:252)
	at com.shipment.erp.controller.MainController.openSystemVariablesWindow(MainController.java:179)
	at com.shipment.erp.controller.MainController.handleNavigationItemSelected(MainController.java:136)
	at com.shipment.erp.controller.MainController.lambda$setupNavigationTree$0(MainController.java:78)
	at javafx.base@21.0.1/com.sun.javafx.event.CompositeEventHandler.dispatchBubblingEvent(CompositeEventHandler.java:86)
	at javafx.base@21.0.1/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:232)
	at javafx.base@21.0.1/com.sun.javafx.event.EventHandlerManager.dispatchBubblingEvent(EventHandlerManager.java:189)
	at javafx.base@21.0.1/com.sun.javafx.event.CompositeEventDispatcher.dispatchBubblingEvent(CompositeEventDispatcher.java:59)
	at javafx.base@21.0.1/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:58)
	at javafx.base@21.0.1/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.1/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.1/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.1/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.1/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.1/com.sun.javafx.event.BasicEventDispatcher.dispatchEvent(BasicEventDispatcher.java:56)
	at javafx.base@21.0.1/com.sun.javafx.event.EventDispatchChainImpl.dispatchEvent(EventDispatchChainImpl.java:114)
	at javafx.base@21.0.1/com.sun.javafx.event.EventUtil.fireEventImpl(EventUtil.java:74)
	at javafx.base@21.0.1/com.sun.javafx.event.EventUtil.fireEvent(EventUtil.java:54)
	at javafx.base@21.0.1/javafx.event.Event.fireEvent(Event.java:198)
	at javafx.graphics@21.0.1/javafx.scene.Scene$ClickGenerator.postProcess(Scene.java:3688)
	at javafx.graphics@21.0.1/javafx.scene.Scene$MouseHandler.process(Scene.java:3993)
	at javafx.graphics@21.0.1/javafx.scene.Scene.processMouseEvent(Scene.java:1890)
	at javafx.graphics@21.0.1/javafx.scene.Scene$ScenePeerListener.mouseEvent(Scene.java:2708)
	at javafx.graphics@21.0.1/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:411)
	at javafx.graphics@21.0.1/com.sun.javafx.tk.quantum.GlassViewEventHandler$MouseEventNotification.run(GlassViewEventHandler.java:301)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at javafx.graphics@21.0.1/com.sun.javafx.tk.quantum.GlassViewEventHandler.lambda$handleMouseEvent$2(GlassViewEventHandler.java:450)
	at javafx.graphics@21.0.1/com.sun.javafx.tk.quantum.QuantumToolkit.runWithoutRenderLock(QuantumToolkit.java:424)
	at javafx.graphics@21.0.1/com.sun.javafx.tk.quantum.GlassViewEventHandler.handleMouseEvent(GlassViewEventHandler.java:449)
	at javafx.graphics@21.0.1/com.sun.glass.ui.View.handleMouseEvent(View.java:551)
	at javafx.graphics@21.0.1/com.sun.glass.ui.View.notifyMouse(View.java:937)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication._runLoop(Native Method)
	at javafx.graphics@21.0.1/com.sun.glass.ui.win.WinApplication.lambda$runLoop$3(WinApplication.java:185)
	at java.base/java.lang.Thread.run(Thread.java:840)
