@echo off
chcp 65001 >nul
set JAVA_TOOL_OPTIONS=-Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=false -Djava.locale.providers=COMPAT,SPI
set NLS_LANG=AMERICAN_AMERICA.AR8MSWIN1256
set LC_ALL=en_US.UTF-8
set LANG=en_US.UTF-8

echo Starting Shipment Management System...

java -cp "target/classes;target/dependency/*" ^
     -Duser.language=en ^
     -Duser.country=US ^
     -Dfile.encoding=UTF-8 ^
     -Djava.locale.providers=COMPAT,SPI ^
     -Doracle.jdbc.defaultNChar=false ^
     -Doracle.jdbc.convertNcharLiterals=false ^
     -Doracle.net.disableOob=true ^
     -Djava.util.logging.config.file=logging.properties ^
     --module-path "E:\java\javafx-sdk-21.0.1\lib" ^
     --add-modules javafx.controls,javafx.fxml ^
     com.shipment.erp.ShipmentManagementApp

pause
