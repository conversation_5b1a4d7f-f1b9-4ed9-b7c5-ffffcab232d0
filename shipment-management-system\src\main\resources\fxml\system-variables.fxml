<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<!--
شاشة المتغيرات العامة للبرنامج
System Variables Screen

تحتوي على جميع الإعدادات العامة للنظام
Contains all general system settings
-->

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" 
            fx:controller="com.shipment.erp.controller.SystemVariablesController"
            styleClass="system-variables-container">

   <!-- شريط العنوان -->
   <!-- Title Bar -->
   <top>
      <VBox styleClass="title-section">
         <padding>
            <Insets bottom="15" left="20" right="20" top="15" />
         </padding>
         
         <!-- العنوان الرئيسي -->
         <!-- Main Title -->
         <HBox alignment="CENTER_LEFT" spacing="10">
            <Label text="المتغيرات العامة للبرنامج" styleClass="main-title">
               <font>
                  <Font name="Tahoma Bold" size="18" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lastUpdateLabel" text="آخر تحديث: --" styleClass="last-update-label" />
         </HBox>
         
         <!-- الوصف -->
         <!-- Description -->
         <Label text="إعداد المتغيرات والثوابت العامة المستخدمة في جميع أنحاء النظام" 
               styleClass="description-label" wrapText="true">
            <VBox.margin>
               <Insets top="5" />
            </VBox.margin>
         </Label>
         
         <Separator>
            <VBox.margin>
               <Insets top="10" />
            </VBox.margin>
         </Separator>
      </VBox>
   </top>

   <!-- المحتوى الرئيسي -->
   <!-- Main Content -->
   <center>
      <ScrollPane fitToWidth="true" styleClass="main-scroll-pane">
         <content>
            <VBox spacing="20" styleClass="content-container">
               <padding>
                  <Insets bottom="20" left="20" right="20" top="20" />
               </padding>
               
               <!-- قسم معلومات الشركة -->
               <!-- Company Information Section -->
               <TitledPane text="معلومات الشركة الأساسية" expanded="true" styleClass="section-pane">
                  <content>
                     <GridPane hgap="15" vgap="12" styleClass="form-grid">
                        <columnConstraints>
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <!-- اسم الشركة -->
                        <Label text="اسم الشركة:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="companyNameField" promptText="أدخل اسم الشركة" 
                                 GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <!-- اسم الشركة بالإنجليزية -->
                        <Label text="اسم الشركة (إنجليزي):" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <TextField fx:id="companyNameEnField" promptText="Enter company name in English" 
                                 GridPane.columnIndex="3" GridPane.rowIndex="0" />
                        
                        <!-- رقم السجل التجاري -->
                        <Label text="رقم السجل التجاري:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="commercialRegField" promptText="أدخل رقم السجل التجاري" 
                                 GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <!-- الرقم الضريبي -->
                        <Label text="الرقم الضريبي:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <TextField fx:id="taxNumberField" promptText="أدخل الرقم الضريبي" 
                                 GridPane.columnIndex="3" GridPane.rowIndex="1" />
                        
                        <!-- العنوان -->
                        <Label text="العنوان:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextArea fx:id="addressArea" promptText="أدخل عنوان الشركة" prefRowCount="2"
                                GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="2" />
                        
                        <!-- الهاتف والفاكس -->
                        <Label text="الهاتف:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="3" />
                        <TextField fx:id="phoneField" promptText="أدخل رقم الهاتف" 
                                 GridPane.columnIndex="1" GridPane.rowIndex="3" />
                        
                        <Label text="الفاكس:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="3" />
                        <TextField fx:id="faxField" promptText="أدخل رقم الفاكس" 
                                 GridPane.columnIndex="3" GridPane.rowIndex="3" />
                        
                        <!-- البريد الإلكتروني والموقع -->
                        <Label text="البريد الإلكتروني:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                        <TextField fx:id="emailField" promptText="أدخل البريد الإلكتروني" 
                                 GridPane.columnIndex="1" GridPane.rowIndex="4" />
                        
                        <Label text="الموقع الإلكتروني:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="4" />
                        <TextField fx:id="websiteField" promptText="أدخل الموقع الإلكتروني" 
                                 GridPane.columnIndex="3" GridPane.rowIndex="4" />
                     </GridPane>
                  </content>
               </TitledPane>
               
               <!-- قسم إعدادات النظام -->
               <!-- System Settings Section -->
               <TitledPane text="إعدادات النظام العامة" expanded="true" styleClass="section-pane">
                  <content>
                     <GridPane hgap="15" vgap="12" styleClass="form-grid">
                        <columnConstraints>
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <!-- اللغة الافتراضية -->
                        <Label text="اللغة الافتراضية:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <ComboBox fx:id="defaultLanguageCombo" prefWidth="200" GridPane.columnIndex="1" GridPane.rowIndex="0">
                           <items>
                              <FXCollections fx:factory="observableArrayList">
                                 <String fx:value="العربية" />
                                 <String fx:value="English" />
                              </FXCollections>
                           </items>
                        </ComboBox>
                        
                        <!-- العملة الافتراضية -->
                        <Label text="العملة الافتراضية:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <ComboBox fx:id="defaultCurrencyCombo" prefWidth="200" GridPane.columnIndex="3" GridPane.rowIndex="0">
                           <items>
                              <FXCollections fx:factory="observableArrayList">
                                 <String fx:value="ريال سعودي (SAR)" />
                                 <String fx:value="دولار أمريكي (USD)" />
                                 <String fx:value="يورو (EUR)" />
                              </FXCollections>
                           </items>
                        </ComboBox>
                        
                        <!-- المنطقة الزمنية -->
                        <Label text="المنطقة الزمنية:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <ComboBox fx:id="timezoneCombo" prefWidth="200" GridPane.columnIndex="1" GridPane.rowIndex="1">
                           <items>
                              <FXCollections fx:factory="observableArrayList">
                                 <String fx:value="Asia/Riyadh" />
                                 <String fx:value="UTC" />
                                 <String fx:value="Europe/London" />
                              </FXCollections>
                           </items>
                        </ComboBox>
                        
                        <!-- تنسيق التاريخ -->
                        <Label text="تنسيق التاريخ:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <ComboBox fx:id="dateFormatCombo" prefWidth="200" GridPane.columnIndex="3" GridPane.rowIndex="1">
                           <items>
                              <FXCollections fx:factory="observableArrayList">
                                 <String fx:value="dd/MM/yyyy" />
                                 <String fx:value="MM/dd/yyyy" />
                                 <String fx:value="yyyy-MM-dd" />
                              </FXCollections>
                           </items>
                        </ComboBox>
                        
                        <!-- عدد الأرقام العشرية -->
                        <Label text="عدد الأرقام العشرية:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <Spinner fx:id="decimalPlacesSpinner" min="0" max="6" initialValue="2" prefWidth="100"
                               GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        
                        <!-- حجم الصفحة الافتراضي -->
                        <Label text="حجم الصفحة الافتراضي:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                        <Spinner fx:id="defaultPageSizeSpinner" min="10" max="1000" initialValue="50" prefWidth="100"
                               GridPane.columnIndex="3" GridPane.rowIndex="2" />
                     </GridPane>
                  </content>
               </TitledPane>
               
               <!-- قسم إعدادات الأمان -->
               <!-- Security Settings Section -->
               <TitledPane text="إعدادات الأمان" expanded="false" styleClass="section-pane">
                  <content>
                     <GridPane hgap="15" vgap="12" styleClass="form-grid">
                        <columnConstraints>
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <!-- مدة انتهاء الجلسة -->
                        <Label text="مدة انتهاء الجلسة (دقيقة):" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Spinner fx:id="sessionTimeoutSpinner" min="5" max="480" initialValue="30" prefWidth="100"
                               GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <!-- عدد محاولات تسجيل الدخول -->
                        <Label text="عدد محاولات تسجيل الدخول:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <Spinner fx:id="maxLoginAttemptsSpinner" min="3" max="10" initialValue="5" prefWidth="100"
                               GridPane.columnIndex="3" GridPane.rowIndex="0" />
                        
                        <!-- مدة قفل الحساب -->
                        <Label text="مدة قفل الحساب (دقيقة):" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <Spinner fx:id="accountLockTimeSpinner" min="5" max="1440" initialValue="15" prefWidth="100"
                               GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        
                        <!-- تفعيل التشفير -->
                        <Label text="تفعيل التشفير:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <CheckBox fx:id="encryptionEnabledCheck" text="تفعيل تشفير البيانات الحساسة" 
                                GridPane.columnIndex="3" GridPane.rowIndex="1" />
                     </GridPane>
                  </content>
               </TitledPane>
               
               <!-- قسم إعدادات النسخ الاحتياطي -->
               <!-- Backup Settings Section -->
               <TitledPane text="إعدادات النسخ الاحتياطي" expanded="false" styleClass="section-pane">
                  <content>
                     <GridPane hgap="15" vgap="12" styleClass="form-grid">
                        <columnConstraints>
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                           <ColumnConstraints minWidth="150" prefWidth="200" />
                           <ColumnConstraints minWidth="200" prefWidth="300" hgrow="ALWAYS" />
                        </columnConstraints>
                        
                        <!-- تفعيل النسخ التلقائي -->
                        <Label text="النسخ الاحتياطي التلقائي:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <CheckBox fx:id="autoBackupEnabledCheck" text="تفعيل النسخ الاحتياطي التلقائي" 
                                GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        
                        <!-- تكرار النسخ -->
                        <Label text="تكرار النسخ:" styleClass="field-label" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <ComboBox fx:id="backupFrequencyCombo" prefWidth="200" GridPane.columnIndex="3" GridPane.rowIndex="0">
                           <items>
                              <FXCollections fx:factory="observableArrayList">
                                 <String fx:value="يومي" />
                                 <String fx:value="أسبوعي" />
                                 <String fx:value="شهري" />
                              </FXCollections>
                           </items>
                        </ComboBox>
                        
                        <!-- مجلد النسخ الاحتياطي -->
                        <Label text="مجلد النسخ الاحتياطي:" styleClass="field-label" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <HBox spacing="5" GridPane.columnIndex="1" GridPane.columnSpan="2" GridPane.rowIndex="1">
                           <TextField fx:id="backupPathField" promptText="اختر مجلد النسخ الاحتياطي" HBox.hgrow="ALWAYS" />
                           <Button fx:id="browseBackupPathButton" text="استعراض" onAction="#handleBrowseBackupPath" />
                        </HBox>
                        
                        <!-- عدد النسخ المحفوظة -->
                        <Label text="عدد النسخ المحفوظة:" styleClass="field-label" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                        <Spinner fx:id="backupRetentionSpinner" min="1" max="30" initialValue="7" prefWidth="100"
                               GridPane.columnIndex="3" GridPane.rowIndex="1" />
                     </GridPane>
                  </content>
               </TitledPane>
            </VBox>
         </content>
      </ScrollPane>
   </center>

   <!-- شريط الأزرار -->
   <!-- Button Bar -->
   <bottom>
      <HBox alignment="CENTER_RIGHT" spacing="10" styleClass="button-bar">
         <padding>
            <Insets bottom="15" left="20" right="20" top="15" />
         </padding>
         
         <Button fx:id="resetButton" text="إعادة تعيين" onAction="#handleReset" styleClass="secondary-button" />
         <Button fx:id="testButton" text="اختبار الإعدادات" onAction="#handleTest" styleClass="secondary-button" />
         <Button fx:id="cancelButton" text="إلغاء" onAction="#handleCancel" styleClass="cancel-button" />
         <Button fx:id="saveButton" text="حفظ" onAction="#handleSave" styleClass="primary-button" />
      </HBox>
   </bottom>
</BorderPane>
