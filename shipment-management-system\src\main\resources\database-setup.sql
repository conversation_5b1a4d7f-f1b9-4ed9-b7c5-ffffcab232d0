-- إعد<PERSON> قاعدة البيانات لنظام إدارة الشحنات
-- Database Setup for Shipment Management System
-- Oracle Database with Arabic Support (AR8MSWIN1256)

-- إنشاء مستخدم قاعدة البيانات
-- Create database user
-- يجب تشغيل هذا الجزء بصلاحيات SYSDBA
-- This section should be run with SYSDBA privileges

-- CREATE USER ship_erp IDENTIFIED BY ys123;
-- GRANT CONNECT, RESOURCE, DBA TO ship_erp;
-- GRANT UNLIMITED TABLESPACE TO ship_erp;
-- ALTER SESSION SET NLS_LANGUAGE='ARABIC';
-- ALTER SESSION SET NLS_TERRITORY='EGYPT';
-- ALTER SESSION SET NLS_CHARACTERSET='AR8MSWIN1256';

-- الاتصال بمستخدم ship_erp
-- Connect as ship_erp user

-- إنشاء جدول الشركات
-- Companies table
CREATE TABLE companies (
    company_id NUMBER(10) PRIMARY KEY,
    company_name NVARCHAR2(200) NOT NULL,
    company_name_en VARCHAR2(200),
    tax_number VARCHAR2(50),
    commercial_register VARCHAR2(50),
    address NVARCHAR2(500),
    city NVARCHAR2(100),
    country NVARCHAR2(100),
    phone VARCHAR2(20),
    email VARCHAR2(100),
    website VARCHAR2(200),
    contact_person NVARCHAR2(100),
    is_active NUMBER(1) DEFAULT 1,
    created_date DATE DEFAULT SYSDATE,
    created_by NVARCHAR2(100),
    modified_date DATE,
    modified_by NVARCHAR2(100)
);

-- إنشاء جدول العملاء
-- Customers table
CREATE TABLE customers (
    customer_id NUMBER(10) PRIMARY KEY,
    customer_code VARCHAR2(20) UNIQUE NOT NULL,
    customer_name NVARCHAR2(200) NOT NULL,
    customer_name_en VARCHAR2(200),
    customer_type VARCHAR2(20) CHECK (customer_type IN ('INDIVIDUAL', 'COMPANY')),
    tax_number VARCHAR2(50),
    national_id VARCHAR2(20),
    address NVARCHAR2(500),
    city NVARCHAR2(100),
    country NVARCHAR2(100),
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    email VARCHAR2(100),
    credit_limit NUMBER(15,2) DEFAULT 0,
    current_balance NUMBER(15,2) DEFAULT 0,
    is_active NUMBER(1) DEFAULT 1,
    created_date DATE DEFAULT SYSDATE,
    created_by NVARCHAR2(100),
    modified_date DATE,
    modified_by NVARCHAR2(100)
);

-- إنشاء جدول الموردين
-- Suppliers table
CREATE TABLE suppliers (
    supplier_id NUMBER(10) PRIMARY KEY,
    supplier_code VARCHAR2(20) UNIQUE NOT NULL,
    supplier_name NVARCHAR2(200) NOT NULL,
    supplier_name_en VARCHAR2(200),
    tax_number VARCHAR2(50),
    commercial_register VARCHAR2(50),
    address NVARCHAR2(500),
    city NVARCHAR2(100),
    country NVARCHAR2(100),
    phone VARCHAR2(20),
    mobile VARCHAR2(20),
    email VARCHAR2(100),
    contact_person NVARCHAR2(100),
    payment_terms NUMBER(3) DEFAULT 30,
    is_active NUMBER(1) DEFAULT 1,
    created_date DATE DEFAULT SYSDATE,
    created_by NVARCHAR2(100),
    modified_date DATE,
    modified_by NVARCHAR2(100)
);

-- إنشاء جدول المنتجات
-- Products table
CREATE TABLE products (
    product_id NUMBER(10) PRIMARY KEY,
    product_code VARCHAR2(50) UNIQUE NOT NULL,
    product_name NVARCHAR2(200) NOT NULL,
    product_name_en VARCHAR2(200),
    description NVARCHAR2(1000),
    category NVARCHAR2(100),
    unit_of_measure NVARCHAR2(50),
    weight NUMBER(10,3),
    dimensions VARCHAR2(100),
    hs_code VARCHAR2(20),
    origin_country NVARCHAR2(100),
    unit_price NUMBER(15,2),
    cost_price NUMBER(15,2),
    is_active NUMBER(1) DEFAULT 1,
    created_date DATE DEFAULT SYSDATE,
    created_by NVARCHAR2(100),
    modified_date DATE,
    modified_by NVARCHAR2(100)
);

-- إنشاء جدول الشحنات
-- Shipments table
CREATE TABLE shipments (
    shipment_id NUMBER(10) PRIMARY KEY,
    shipment_number VARCHAR2(50) UNIQUE NOT NULL,
    customer_id NUMBER(10) REFERENCES customers(customer_id),
    supplier_id NUMBER(10) REFERENCES suppliers(supplier_id),
    shipment_type VARCHAR2(20) CHECK (shipment_type IN ('IMPORT', 'EXPORT', 'DOMESTIC')),
    shipment_status VARCHAR2(20) CHECK (shipment_status IN ('PENDING', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED')),
    origin_port NVARCHAR2(100),
    destination_port NVARCHAR2(100),
    departure_date DATE,
    arrival_date DATE,
    estimated_arrival DATE,
    shipping_method VARCHAR2(50),
    container_number VARCHAR2(50),
    seal_number VARCHAR2(50),
    bill_of_lading VARCHAR2(50),
    total_weight NUMBER(10,3),
    total_volume NUMBER(10,3),
    total_value NUMBER(15,2),
    currency VARCHAR2(3) DEFAULT 'USD',
    insurance_value NUMBER(15,2),
    freight_cost NUMBER(15,2),
    customs_value NUMBER(15,2),
    notes NVARCHAR2(1000),
    is_active NUMBER(1) DEFAULT 1,
    created_date DATE DEFAULT SYSDATE,
    created_by NVARCHAR2(100),
    modified_date DATE,
    modified_by NVARCHAR2(100)
);

-- إنشاء جدول تفاصيل الشحنات
-- Shipment details table
CREATE TABLE shipment_details (
    detail_id NUMBER(10) PRIMARY KEY,
    shipment_id NUMBER(10) REFERENCES shipments(shipment_id),
    product_id NUMBER(10) REFERENCES products(product_id),
    quantity NUMBER(10,3) NOT NULL,
    unit_price NUMBER(15,2),
    total_amount NUMBER(15,2),
    weight NUMBER(10,3),
    volume NUMBER(10,3),
    package_type NVARCHAR2(50),
    package_count NUMBER(10),
    notes NVARCHAR2(500),
    created_date DATE DEFAULT SYSDATE,
    created_by NVARCHAR2(100)
);

-- إنشاء جدول المستندات
-- Documents table
CREATE TABLE documents (
    document_id NUMBER(10) PRIMARY KEY,
    shipment_id NUMBER(10) REFERENCES shipments(shipment_id),
    document_type VARCHAR2(50),
    document_name NVARCHAR2(200),
    file_name VARCHAR2(200),
    file_path VARCHAR2(500),
    file_size NUMBER(15),
    mime_type VARCHAR2(100),
    upload_date DATE DEFAULT SYSDATE,
    uploaded_by NVARCHAR2(100),
    is_active NUMBER(1) DEFAULT 1
);

-- إنشاء جدول المستخدمين
-- Users table
CREATE TABLE users (
    user_id NUMBER(10) PRIMARY KEY,
    username VARCHAR2(50) UNIQUE NOT NULL,
    password_hash VARCHAR2(255) NOT NULL,
    full_name NVARCHAR2(100) NOT NULL,
    email VARCHAR2(100),
    phone VARCHAR2(20),
    role VARCHAR2(20) CHECK (role IN ('ADMIN', 'MANAGER', 'OPERATOR', 'VIEWER')),
    is_active NUMBER(1) DEFAULT 1,
    last_login DATE,
    failed_login_attempts NUMBER(3) DEFAULT 0,
    locked_until DATE,
    created_date DATE DEFAULT SYSDATE,
    created_by NVARCHAR2(100),
    modified_date DATE,
    modified_by NVARCHAR2(100)
);

-- إنشاء جدول سجل العمليات
-- Audit log table
CREATE TABLE audit_log (
    log_id NUMBER(15) PRIMARY KEY,
    table_name VARCHAR2(50),
    record_id NUMBER(15),
    operation VARCHAR2(10) CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
    old_values CLOB,
    new_values CLOB,
    changed_by NVARCHAR2(100),
    change_date DATE DEFAULT SYSDATE,
    ip_address VARCHAR2(45),
    user_agent VARCHAR2(500)
);

-- إنشاء المتسلسلات
-- Create sequences
CREATE SEQUENCE seq_companies START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_customers START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_suppliers START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_products START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_shipments START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_shipment_details START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_documents START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_users START WITH 1 INCREMENT BY 1;
CREATE SEQUENCE seq_audit_log START WITH 1 INCREMENT BY 1;

-- إنشاء الفهارس
-- Create indexes
CREATE INDEX idx_customers_code ON customers(customer_code);
CREATE INDEX idx_customers_name ON customers(customer_name);
CREATE INDEX idx_suppliers_code ON suppliers(supplier_code);
CREATE INDEX idx_suppliers_name ON suppliers(supplier_name);
CREATE INDEX idx_products_code ON products(product_code);
CREATE INDEX idx_products_name ON products(product_name);
CREATE INDEX idx_shipments_number ON shipments(shipment_number);
CREATE INDEX idx_shipments_customer ON shipments(customer_id);
CREATE INDEX idx_shipments_status ON shipments(shipment_status);
CREATE INDEX idx_shipments_date ON shipments(created_date);
CREATE INDEX idx_shipment_details_shipment ON shipment_details(shipment_id);
CREATE INDEX idx_documents_shipment ON documents(shipment_id);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_audit_log_table ON audit_log(table_name, record_id);
CREATE INDEX idx_audit_log_date ON audit_log(change_date);

-- إدراج بيانات أولية
-- Insert initial data

-- إدراج مستخدم إداري افتراضي
-- Insert default admin user
INSERT INTO users (user_id, username, password_hash, full_name, email, role, created_by)
VALUES (seq_users.NEXTVAL, 'admin', 
        'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- SHA-256 hash of 'admin123'
        'مدير النظام', '<EMAIL>', 'ADMIN', 'SYSTEM');

-- إدراج شركة افتراضية
-- Insert default company
INSERT INTO companies (company_id, company_name, company_name_en, address, city, country, 
                      phone, email, created_by)
VALUES (seq_companies.NEXTVAL, 'شركة الشحن المتقدم', 'Advanced Shipping Company',
        'شارع التحرير، وسط البلد', 'القاهرة', 'مصر',
        '+20-2-12345678', '<EMAIL>', 'SYSTEM');

COMMIT;
