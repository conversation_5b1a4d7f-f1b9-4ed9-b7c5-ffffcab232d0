@echo off
REM إعداد قاعدة البيانات Oracle لنظام إدارة الشحنات
REM Oracle Database Setup for Shipment Management System

echo ========================================
echo إعداد قاعدة البيانات Oracle
echo Oracle Database Setup
echo ========================================

REM التحقق من وجود Oracle
echo التحقق من تثبيت Oracle...
echo Checking Oracle installation...

sqlplus -v >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Oracle غير مثبت أو غير متاح في PATH
    echo Error: Oracle is not installed or not available in PATH
    echo يرجى تثبيت Oracle Database أو Oracle Instant Client
    echo Please install Oracle Database or Oracle Instant Client
    pause
    exit /b 1
)

echo تم العثور على Oracle
echo Oracle found

REM إنشاء مستخدم قاعدة البيانات
echo ========================================
echo إنشاء مستخدم قاعدة البيانات
echo Creating database user
echo ========================================

echo يرجى إدخال كلمة مرور مدير النظام (SYS):
echo Please enter SYS password:
set /p SYS_PASSWORD=

echo إنشاء مستخدم ship_erp...
echo Creating user ship_erp...

sqlplus sys/%SYS_PASSWORD%@localhost:1521/XE as sysdba @create_user.sql

if %errorlevel% neq 0 (
    echo خطأ في إنشاء المستخدم
    echo Error creating user
    pause
    exit /b 1
)

echo تم إنشاء المستخدم بنجاح
echo User created successfully

REM إنشاء الجداول
echo ========================================
echo إنشاء الجداول والبيانات الأولية
echo Creating tables and initial data
echo ========================================

sqlplus ship_erp/ys123@localhost:1521/XE @src/main/resources/database-setup.sql

if %errorlevel% neq 0 (
    echo خطأ في إنشاء الجداول
    echo Error creating tables
    pause
    exit /b 1
)

echo تم إنشاء الجداول بنجاح
echo Tables created successfully

REM اختبار الاتصال
echo ========================================
echo اختبار الاتصال بقاعدة البيانات
echo Testing database connection
echo ========================================

sqlplus ship_erp/ys123@localhost:1521/XE @test_connection.sql

if %errorlevel% neq 0 (
    echo خطأ في اختبار الاتصال
    echo Error testing connection
    pause
    exit /b 1
)

echo ========================================
echo تم إعداد قاعدة البيانات بنجاح!
echo Database setup completed successfully!
echo ========================================
echo.
echo معلومات الاتصال:
echo Connection details:
echo الخادم: localhost:1521/XE
echo Server: localhost:1521/XE
echo المستخدم: ship_erp
echo Username: ship_erp
echo كلمة المرور: ys123
echo Password: ys123
echo.
pause
