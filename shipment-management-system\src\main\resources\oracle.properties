# Oracle JDBC Configuration to fix regex issues with Arabic characters
# إعدادات Oracle JDBC لحل مشاكل regex مع الأحرف العربية

# Disable NCHAR support to avoid regex pattern issues
oracle.jdbc.defaultNChar=false
oracle.jdbc.convertNcharLiterals=false

# Disable out-of-band breaks
oracle.net.disableOob=true

# Force English locale for Oracle operations
oracle.jdbc.locale=en_US

# Connection timeout settings
oracle.net.CONNECT_TIMEOUT=10000
oracle.jdbc.ReadTimeout=30000
oracle.net.READ_TIMEOUT=30000

# Disable problematic Oracle features
oracle.jdbc.autoCommitSpecCompliant=false
oracle.jdbc.implicitStatementCacheSize=0
