@echo off
REM تشغيل نظام إدارة الشحنات مع إعدادات Oracle الصحيحة
REM Run Shipment Management System with correct Oracle settings

echo ========================================
echo نظام إدارة الشحنات
echo Shipment Management System
echo ========================================

REM تعيين متغيرات البيئة لحل مشكلة Oracle مع الأحرف العربية
REM Set environment variables to fix Oracle issue with Arabic characters
set JAVA_TOOL_OPTIONS=-Duser.language=en -Duser.country=US -Dfile.encoding=UTF-8 -Doracle.jdbc.defaultNChar=true
set NLS_LANG=AMERICAN_AMERICA.AL32UTF8

echo تعيين إعدادات البيئة...
echo Setting environment variables...
echo JAVA_TOOL_OPTIONS=%JAVA_TOOL_OPTIONS%
echo NLS_LANG=%NLS_LANG%
echo.

REM التحقق من وجود Java
echo التحقق من تثبيت Java...
echo Checking Java installation...

java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Java غير مثبت أو غير متاح في PATH
    echo Error: Java is not installed or not available in PATH
    echo يرجى تثبيت Java 17 أو أحدث
    echo Please install Java 17 or newer
    pause
    exit /b 1
)

echo تم العثور على Java
echo Java found

REM التحقق من وجود Maven
echo التحقق من تثبيت Maven...
echo Checking Maven installation...

E:\java\apache-maven-3.9.11\bin\mvn.cmd -version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Maven غير متاح
    echo Error: Maven not available
    echo يرجى التأكد من مسار Maven
    echo Please check Maven path
    pause
    exit /b 1
)

echo تم العثور على Maven
echo Maven found

REM تجميع المشروع
echo ========================================
echo تجميع المشروع...
echo Compiling project...
echo ========================================

E:\java\apache-maven-3.9.11\bin\mvn.cmd clean compile

if %errorlevel% neq 0 (
    echo خطأ في تجميع المشروع
    echo Error compiling project
    pause
    exit /b 1
)

echo تم تجميع المشروع بنجاح
echo Project compiled successfully

REM تشغيل النظام
echo ========================================
echo تشغيل نظام إدارة الشحنات...
echo Starting Shipment Management System...
echo ========================================
echo.
echo معلومات تسجيل الدخول:
echo Login credentials:
echo اسم المستخدم: admin
echo Username: admin
echo كلمة المرور: admin123
echo Password: admin123
echo.

REM تشغيل التطبيق مع إعدادات JavaFX
java -cp "target/classes;target/dependency/*" ^
     -Duser.language=en ^
     -Duser.country=US ^
     -Dfile.encoding=UTF-8 ^
     -Doracle.jdbc.defaultNChar=true ^
     --module-path "E:\java\javafx-sdk-21.0.1\lib" ^
     --add-modules javafx.controls,javafx.fxml ^
     --add-exports javafx.graphics/com.sun.javafx.application=ALL-UNNAMED ^
     com.shipment.erp.ShipmentManagementApp

echo.
echo تم إغلاق النظام
echo System closed
pause
