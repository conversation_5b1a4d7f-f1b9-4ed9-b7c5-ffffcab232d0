/* 
 * أنماط RTL لنظام إدارة الشحنات
 * RTL Styles for Shipment Management System
 * 
 * يوفر دعم كامل للغة العربية مع محاذاة من اليمين لليسار
 * Provides full Arabic language support with right-to-left alignment
 */

/* ===== الإعدادات العامة لـ RTL ===== */
/* ===== General RTL Settings ===== */
.root {
    -fx-node-orientation: right-to-left;
}

/* ===== النصوص والتسميات ===== */
/* ===== Text and Labels ===== */
.label, .text, .text-field, .text-area, .password-field {
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

.label {
    -fx-content-display: right;
    -fx-text-alignment: right;
    -fx-alignment: center-right;
}

/* ===== حقول الإدخال ===== */
/* ===== Input Fields ===== */
.text-field, .text-area, .password-field {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.text-field .text, .text-area .text, .password-field .text {
    -fx-text-alignment: right;
}

/* ===== القوائم المنسدلة ===== */
/* ===== ComboBoxes ===== */
.combo-box {
    -fx-alignment: center-right;
}

.combo-box .list-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.combo-box .arrow-button {
    -fx-alignment: center-left;
}

.combo-box .arrow {
    -fx-alignment: center-left;
}

/* ===== الجداول ===== */
/* ===== Tables ===== */
.table-view .column-header {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.table-view .column-header .label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.table-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.table-cell .text {
    -fx-text-alignment: right;
}

/* محاذاة خاصة للأعمدة الرقمية */
/* Special alignment for numeric columns */
.table-cell.numeric {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

.table-cell.currency {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

.table-cell.date {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

/* ===== القوائم ===== */
/* ===== Lists ===== */
.list-view .list-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.list-view .list-cell .text {
    -fx-text-alignment: right;
}

/* ===== الأشجار ===== */
/* ===== Trees ===== */
.tree-view .tree-cell {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.tree-view .tree-cell .text {
    -fx-text-alignment: right;
}

.tree-view .tree-cell .tree-disclosure-node {
    -fx-alignment: center-left;
}

/* ===== القوائم ===== */
/* ===== Menus ===== */
.menu-bar .menu {
    -fx-alignment: center-right;
}

.menu-bar .menu .label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.menu-item {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.menu-item .label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.context-menu .menu-item {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

/* ===== الأزرار ===== */
/* ===== Buttons ===== */
.button {
    -fx-alignment: center;
    -fx-content-display: right;
}

.button .text {
    -fx-text-alignment: center;
}

/* أزرار مع أيقونات */
/* Buttons with icons */
.button.icon-left {
    -fx-content-display: left;
    -fx-graphic-text-gap: 8px;
}

.button.icon-right {
    -fx-content-display: right;
    -fx-graphic-text-gap: 8px;
}

.button.icon-top {
    -fx-content-display: top;
    -fx-graphic-text-gap: 4px;
}

.button.icon-bottom {
    -fx-content-display: bottom;
    -fx-graphic-text-gap: 4px;
}

/* ===== مربعات الاختيار ===== */
/* ===== CheckBoxes ===== */
.check-box {
    -fx-alignment: center-right;
    -fx-content-display: right;
    -fx-graphic-text-gap: 8px;
}

.check-box .text {
    -fx-text-alignment: right;
}

/* ===== أزرار الراديو ===== */
/* ===== Radio Buttons ===== */
.radio-button {
    -fx-alignment: center-right;
    -fx-content-display: right;
    -fx-graphic-text-gap: 8px;
}

.radio-button .text {
    -fx-text-alignment: right;
}

/* ===== التبويبات ===== */
/* ===== Tabs ===== */
.tab-pane .tab {
    -fx-alignment: center-right;
}

.tab-pane .tab .tab-label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.tab-pane .tab .tab-close-button {
    -fx-alignment: center-left;
}

/* ===== الألواح المعنونة ===== */
/* ===== Titled Panes ===== */
.titled-pane > .title {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.titled-pane > .title .text {
    -fx-text-alignment: right;
}

.titled-pane > .title .arrow-button {
    -fx-alignment: center-left;
}

/* ===== الأكورديون ===== */
/* ===== Accordion ===== */
.accordion .titled-pane > .title {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

/* ===== شريط الأدوات ===== */
/* ===== Tool Bar ===== */
.tool-bar {
    -fx-alignment: center-right;
}

.tool-bar .button {
    -fx-alignment: center;
}

/* ===== شريط الحالة ===== */
/* ===== Status Bar ===== */
.status-bar {
    -fx-alignment: center-right;
}

.status-bar .label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

/* ===== الحوارات والتنبيهات ===== */
/* ===== Dialogs and Alerts ===== */
.dialog-pane {
    -fx-alignment: center-right;
}

.dialog-pane .header-panel {
    -fx-alignment: center-right;
}

.dialog-pane .header-panel .label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.dialog-pane .content {
    -fx-alignment: center-right;
}

.dialog-pane .content .label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.dialog-pane .button-bar {
    -fx-alignment: center-left;
}

/* ===== التخطيطات ===== */
/* ===== Layouts ===== */
.hbox {
    -fx-alignment: center-right;
}

.vbox {
    -fx-alignment: top-right;
}

.grid-pane {
    -fx-alignment: top-right;
}

.border-pane {
    -fx-alignment: center-right;
}

.flow-pane {
    -fx-alignment: top-right;
}

.tile-pane {
    -fx-alignment: top-right;
}

/* ===== النماذج ===== */
/* ===== Forms ===== */
.form-container {
    -fx-alignment: top-right;
    -fx-spacing: 10px;
    -fx-padding: 20px;
}

.form-row {
    -fx-alignment: center-right;
    -fx-spacing: 10px;
}

.form-label {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-min-width: 120px;
    -fx-pref-width: 120px;
}

.form-field {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-min-width: 200px;
    -fx-pref-width: 200px;
}

/* ===== الأرقام والعملات ===== */
/* ===== Numbers and Currencies ===== */
.numeric-field {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

.currency-field {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

.percentage-field {
    -fx-alignment: center-left;
    -fx-text-alignment: left;
}

/* ===== التواريخ والأوقات ===== */
/* ===== Dates and Times ===== */
.date-picker {
    -fx-alignment: center-right;
}

.date-picker .text-field {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
}

.date-picker .arrow-button {
    -fx-alignment: center-left;
}

/* ===== الرموز والأيقونات ===== */
/* ===== Icons and Symbols ===== */
.icon {
    -fx-alignment: center;
}

.icon-text {
    -fx-alignment: center-right;
    -fx-content-display: right;
    -fx-graphic-text-gap: 8px;
}

/* ===== التمرير ===== */
/* ===== Scrolling ===== */
.scroll-pane {
    -fx-fit-to-width: true;
    -fx-fit-to-height: true;
}

.scroll-pane .viewport {
    -fx-alignment: top-right;
}

/* ===== الجداول المتقدمة ===== */
/* ===== Advanced Tables ===== */
.table-view .filler {
    -fx-alignment: center-right;
}

.table-view .column-resize-line {
    -fx-alignment: center-right;
}

.table-view .column-drag-header {
    -fx-alignment: center-right;
}

/* ===== القوائم المتقدمة ===== */
/* ===== Advanced Lists ===== */
.list-view .virtual-flow {
    -fx-alignment: top-right;
}

.list-view .clipped-container {
    -fx-alignment: top-right;
}

/* ===== التحديد المتعدد ===== */
/* ===== Multiple Selection ===== */
.list-view:multi-selection .list-cell:selected {
    -fx-background-color: #0078d4;
    -fx-text-fill: white;
}

.table-view:multi-selection .table-row-cell:selected {
    -fx-background-color: #0078d4;
    -fx-text-fill: white;
}

/* ===== الفلاتر والبحث ===== */
/* ===== Filters and Search ===== */
.search-field {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-prompt-text-fill: #999999;
}

.filter-field {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-prompt-text-fill: #999999;
}

/* ===== التحقق من الصحة ===== */
/* ===== Validation ===== */
.validation-error {
    -fx-border-color: #dc3545;
    -fx-border-width: 2px;
    -fx-background-color: #fff5f5;
}

.validation-warning {
    -fx-border-color: #ffc107;
    -fx-border-width: 2px;
    -fx-background-color: #fffbf0;
}

.validation-success {
    -fx-border-color: #28a745;
    -fx-border-width: 2px;
    -fx-background-color: #f0fff4;
}

/* ===== الرسائل المضمنة ===== */
/* ===== Inline Messages ===== */
.inline-message {
    -fx-alignment: center-right;
    -fx-text-alignment: right;
    -fx-font-size: 11px;
    -fx-padding: 4px 8px;
    -fx-background-radius: 4px;
}

.inline-message.error {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-border-color: #f5c6cb;
}

.inline-message.warning {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-border-color: #ffeaa7;
}

.inline-message.success {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-border-color: #c3e6cb;
}

.inline-message.info {
    -fx-background-color: #d1ecf1;
    -fx-text-fill: #0c5460;
    -fx-border-color: #bee5eb;
}
