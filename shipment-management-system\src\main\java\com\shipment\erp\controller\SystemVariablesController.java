package com.shipment.erp.controller;

import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.stage.DirectoryChooser;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * تحكم شاشة المتغيرات العامة للبرنامج
 * System Variables Controller
 * 
 * يدير جميع الإعدادات العامة للنظام
 * Manages all general system settings
 */
public class SystemVariablesController implements Initializable {
    
    private static final Logger logger = LoggerFactory.getLogger(SystemVariablesController.class);
    
    // Company Information Fields
    @FXML private TextField companyNameField;
    @FXML private TextField companyNameEnField;
    @FXML private TextField commercialRegField;
    @FXML private TextField taxNumberField;
    @FXML private TextArea addressArea;
    @FXML private TextField phoneField;
    @FXML private TextField faxField;
    @FXML private TextField emailField;
    @FXML private TextField websiteField;
    
    // System Settings Fields
    @FXML private ComboBox<String> defaultLanguageCombo;
    @FXML private ComboBox<String> defaultCurrencyCombo;
    @FXML private ComboBox<String> timezoneCombo;
    @FXML private ComboBox<String> dateFormatCombo;
    @FXML private Spinner<Integer> decimalPlacesSpinner;
    @FXML private Spinner<Integer> defaultPageSizeSpinner;
    
    // Security Settings Fields
    @FXML private Spinner<Integer> sessionTimeoutSpinner;
    @FXML private Spinner<Integer> maxLoginAttemptsSpinner;
    @FXML private Spinner<Integer> accountLockTimeSpinner;
    @FXML private CheckBox encryptionEnabledCheck;
    
    // Backup Settings Fields
    @FXML private CheckBox autoBackupEnabledCheck;
    @FXML private ComboBox<String> backupFrequencyCombo;
    @FXML private TextField backupPathField;
    @FXML private Button browseBackupPathButton;
    @FXML private Spinner<Integer> backupRetentionSpinner;
    
    // Control Fields
    @FXML private Label lastUpdateLabel;
    @FXML private Button resetButton;
    @FXML private Button testButton;
    @FXML private Button cancelButton;
    @FXML private Button saveButton;
    
    // Data
    private boolean hasUnsavedChanges = false;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        logger.info("تهيئة شاشة المتغيرات العامة - Initializing system variables screen");
        
        try {
            setupFields();
            loadCurrentSettings();
            setupEventHandlers();
            updateLastUpdateLabel();
            
            logger.info("تم تهيئة شاشة المتغيرات العامة بنجاح - System variables screen initialized successfully");
        } catch (Exception e) {
            logger.error("خطأ في تهيئة شاشة المتغيرات العامة - Error initializing system variables screen", e);
            showErrorAlert("خطأ في التهيئة", "حدث خطأ أثناء تهيئة الشاشة: " + e.getMessage());
        }
    }
    
    /**
     * إعداد الحقول والعناصر
     * Setup fields and components
     */
    private void setupFields() {
        // إعداد Spinners
        // Setup Spinners
        setupSpinner(decimalPlacesSpinner, 0, 6, 2);
        setupSpinner(defaultPageSizeSpinner, 10, 1000, 50);
        setupSpinner(sessionTimeoutSpinner, 5, 480, 30);
        setupSpinner(maxLoginAttemptsSpinner, 3, 10, 5);
        setupSpinner(accountLockTimeSpinner, 5, 1440, 15);
        setupSpinner(backupRetentionSpinner, 1, 30, 7);
        
        // إعداد ComboBoxes
        // Setup ComboBoxes
        defaultLanguageCombo.setValue("العربية");
        defaultCurrencyCombo.setValue("ريال سعودي (SAR)");
        timezoneCombo.setValue("Asia/Riyadh");
        dateFormatCombo.setValue("dd/MM/yyyy");
        backupFrequencyCombo.setValue("يومي");
        
        // إعداد CheckBoxes
        // Setup CheckBoxes
        encryptionEnabledCheck.setSelected(true);
        autoBackupEnabledCheck.setSelected(false);
    }
    
    /**
     * إعداد Spinner
     * Setup spinner
     */
    private void setupSpinner(Spinner<Integer> spinner, int min, int max, int initialValue) {
        SpinnerValueFactory<Integer> valueFactory = new SpinnerValueFactory.IntegerSpinnerValueFactory(min, max, initialValue);
        spinner.setValueFactory(valueFactory);
        spinner.setEditable(true);
    }
    
    /**
     * تحميل الإعدادات الحالية
     * Load current settings
     */
    private void loadCurrentSettings() {
        try {
            logger.info("تحميل الإعدادات الحالية - Loading current settings");
            
            // تحميل معلومات الشركة
            // Load company information
            companyNameField.setText("شركة الشحنات المتقدمة");
            companyNameEnField.setText("Advanced Shipping Company");
            commercialRegField.setText("1010123456");
            taxNumberField.setText("300123456789003");
            addressArea.setText("الرياض، المملكة العربية السعودية\nص.ب 12345");
            phoneField.setText("+966-11-1234567");
            faxField.setText("+966-11-1234568");
            emailField.setText("<EMAIL>");
            websiteField.setText("www.advancedshipping.com");
            
            // تحميل إعدادات النظام
            // Load system settings
            // القيم الافتراضية محددة في setupFields()
            
            // تحميل إعدادات الأمان
            // Load security settings
            // القيم الافتراضية محددة في setupFields()
            
            // تحميل إعدادات النسخ الاحتياطي
            // Load backup settings
            backupPathField.setText("C:\\Backups\\ShipmentERP");
            
            logger.info("تم تحميل الإعدادات بنجاح - Settings loaded successfully");
            
        } catch (Exception e) {
            logger.error("خطأ في تحميل الإعدادات - Error loading settings", e);
            showErrorAlert("خطأ في التحميل", "حدث خطأ أثناء تحميل الإعدادات: " + e.getMessage());
        }
    }
    
    /**
     * إعداد معالجات الأحداث
     * Setup event handlers
     */
    private void setupEventHandlers() {
        // معالجات تغيير البيانات
        // Data change handlers
        companyNameField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        companyNameEnField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        commercialRegField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        taxNumberField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        addressArea.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        phoneField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        faxField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        emailField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        websiteField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        
        defaultLanguageCombo.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        defaultCurrencyCombo.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        timezoneCombo.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        dateFormatCombo.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        
        decimalPlacesSpinner.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        defaultPageSizeSpinner.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        sessionTimeoutSpinner.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        maxLoginAttemptsSpinner.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        accountLockTimeSpinner.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        backupRetentionSpinner.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        
        encryptionEnabledCheck.selectedProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        autoBackupEnabledCheck.selectedProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        backupFrequencyCombo.valueProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
        backupPathField.textProperty().addListener((obs, oldVal, newVal) -> markAsChanged());
    }
    
    /**
     * تحديث تسمية آخر تحديث
     * Update last update label
     */
    private void updateLastUpdateLabel() {
        String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        lastUpdateLabel.setText("آخر تحديث: " + currentTime);
    }
    
    /**
     * تمييز التغييرات غير المحفوظة
     * Mark as changed
     */
    private void markAsChanged() {
        if (!hasUnsavedChanges) {
            hasUnsavedChanges = true;
            saveButton.setStyle("-fx-background-color: #ff6b6b; -fx-text-fill: white;");
        }
    }
    
    // Event Handlers
    
    @FXML
    private void handleBrowseBackupPath() {
        try {
            DirectoryChooser directoryChooser = new DirectoryChooser();
            directoryChooser.setTitle("اختر مجلد النسخ الاحتياطي");
            
            // تعيين المجلد الحالي إذا كان موجوداً
            // Set current directory if exists
            String currentPath = backupPathField.getText();
            if (currentPath != null && !currentPath.trim().isEmpty()) {
                File currentDir = new File(currentPath);
                if (currentDir.exists() && currentDir.isDirectory()) {
                    directoryChooser.setInitialDirectory(currentDir);
                }
            }
            
            Stage stage = (Stage) browseBackupPathButton.getScene().getWindow();
            File selectedDirectory = directoryChooser.showDialog(stage);
            
            if (selectedDirectory != null) {
                backupPathField.setText(selectedDirectory.getAbsolutePath());
                logger.info("تم اختيار مجلد النسخ الاحتياطي: {}", selectedDirectory.getAbsolutePath());
            }
            
        } catch (Exception e) {
            logger.error("خطأ في اختيار مجلد النسخ الاحتياطي - Error selecting backup directory", e);
            showErrorAlert("خطأ", "حدث خطأ أثناء اختيار المجلد: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleReset() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("تأكيد إعادة التعيين");
        alert.setHeaderText("هل تريد إعادة تعيين جميع الإعدادات؟");
        alert.setContentText("سيتم فقدان جميع التغييرات غير المحفوظة.");
        
        Optional<ButtonType> result = alert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            loadCurrentSettings();
            hasUnsavedChanges = false;
            saveButton.setStyle("");
            logger.info("تم إعادة تعيين الإعدادات - Settings reset");
        }
    }
    
    @FXML
    private void handleTest() {
        try {
            logger.info("اختبار الإعدادات - Testing settings");
            
            // اختبار الإعدادات
            // Test settings
            boolean testResult = testCurrentSettings();
            
            if (testResult) {
                showInfoAlert("نتيجة الاختبار", "جميع الإعدادات صحيحة ويمكن تطبيقها.");
            } else {
                showWarningAlert("نتيجة الاختبار", "توجد بعض المشاكل في الإعدادات. يرجى المراجعة.");
            }
            
        } catch (Exception e) {
            logger.error("خطأ في اختبار الإعدادات - Error testing settings", e);
            showErrorAlert("خطأ في الاختبار", "حدث خطأ أثناء اختبار الإعدادات: " + e.getMessage());
        }
    }
    
    @FXML
    private void handleCancel() {
        if (hasUnsavedChanges) {
            Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("تأكيد الإلغاء");
            alert.setHeaderText("توجد تغييرات غير محفوظة");
            alert.setContentText("هل تريد إغلاق النافذة بدون حفظ التغييرات؟");
            
            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() != ButtonType.OK) {
                return;
            }
        }
        
        // إغلاق النافذة
        // Close window
        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }
    
    @FXML
    private void handleSave() {
        try {
            logger.info("حفظ الإعدادات - Saving settings");
            
            // التحقق من صحة البيانات
            // Validate data
            if (!validateSettings()) {
                return;
            }
            
            // حفظ الإعدادات
            // Save settings
            boolean saveResult = saveSettings();
            
            if (saveResult) {
                hasUnsavedChanges = false;
                saveButton.setStyle("");
                updateLastUpdateLabel();
                showInfoAlert("تم الحفظ", "تم حفظ الإعدادات بنجاح.");
                logger.info("تم حفظ الإعدادات بنجاح - Settings saved successfully");
            } else {
                showErrorAlert("خطأ في الحفظ", "فشل في حفظ الإعدادات. يرجى المحاولة مرة أخرى.");
            }
            
        } catch (Exception e) {
            logger.error("خطأ في حفظ الإعدادات - Error saving settings", e);
            showErrorAlert("خطأ في الحفظ", "حدث خطأ أثناء حفظ الإعدادات: " + e.getMessage());
        }
    }

    // Helper Methods

    /**
     * التحقق من صحة الإعدادات
     * Validate settings
     */
    private boolean validateSettings() {
        // التحقق من اسم الشركة
        // Validate company name
        if (companyNameField.getText() == null || companyNameField.getText().trim().isEmpty()) {
            showErrorAlert("خطأ في البيانات", "يرجى إدخال اسم الشركة");
            companyNameField.requestFocus();
            return false;
        }

        // التحقق من البريد الإلكتروني
        // Validate email
        String email = emailField.getText();
        if (email != null && !email.trim().isEmpty()) {
            if (!isValidEmail(email)) {
                showErrorAlert("خطأ في البيانات", "البريد الإلكتروني غير صحيح");
                emailField.requestFocus();
                return false;
            }
        }

        // التحقق من مجلد النسخ الاحتياطي
        // Validate backup path
        if (autoBackupEnabledCheck.isSelected()) {
            String backupPath = backupPathField.getText();
            if (backupPath == null || backupPath.trim().isEmpty()) {
                showErrorAlert("خطأ في البيانات", "يرجى تحديد مجلد النسخ الاحتياطي");
                backupPathField.requestFocus();
                return false;
            }

            File backupDir = new File(backupPath);
            if (!backupDir.exists()) {
                Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
                alert.setTitle("تأكيد إنشاء المجلد");
                alert.setHeaderText("المجلد غير موجود");
                alert.setContentText("هل تريد إنشاء المجلد: " + backupPath + "؟");

                Optional<ButtonType> result = alert.showAndWait();
                if (result.isPresent() && result.get() == ButtonType.OK) {
                    try {
                        backupDir.mkdirs();
                    } catch (Exception e) {
                        showErrorAlert("خطأ", "فشل في إنشاء المجلد: " + e.getMessage());
                        return false;
                    }
                } else {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * التحقق من صحة البريد الإلكتروني
     * Validate email format
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailRegex);
    }

    /**
     * اختبار الإعدادات الحالية
     * Test current settings
     */
    private boolean testCurrentSettings() {
        try {
            // اختبار الاتصال بقاعدة البيانات
            // Test database connection
            // TODO: إضافة اختبار قاعدة البيانات

            // اختبار مجلد النسخ الاحتياطي
            // Test backup directory
            if (autoBackupEnabledCheck.isSelected()) {
                String backupPath = backupPathField.getText();
                if (backupPath != null && !backupPath.trim().isEmpty()) {
                    File backupDir = new File(backupPath);
                    if (!backupDir.exists() || !backupDir.canWrite()) {
                        logger.warn("مجلد النسخ الاحتياطي غير متاح للكتابة - Backup directory not writable");
                        return false;
                    }
                }
            }

            // اختبار البريد الإلكتروني
            // Test email format
            String email = emailField.getText();
            if (email != null && !email.trim().isEmpty()) {
                if (!isValidEmail(email)) {
                    logger.warn("تنسيق البريد الإلكتروني غير صحيح - Invalid email format");
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            logger.error("خطأ في اختبار الإعدادات - Error testing settings", e);
            return false;
        }
    }

    /**
     * حفظ الإعدادات
     * Save settings
     */
    private boolean saveSettings() {
        try {
            // حفظ معلومات الشركة
            // Save company information
            // TODO: حفظ في قاعدة البيانات أو ملف الإعدادات

            // حفظ إعدادات النظام
            // Save system settings
            // TODO: حفظ في قاعدة البيانات أو ملف الإعدادات

            // حفظ إعدادات الأمان
            // Save security settings
            // TODO: حفظ في قاعدة البيانات أو ملف الإعدادات

            // حفظ إعدادات النسخ الاحتياطي
            // Save backup settings
            // TODO: حفظ في قاعدة البيانات أو ملف الإعدادات

            logger.info("تم حفظ جميع الإعدادات - All settings saved");
            return true;

        } catch (Exception e) {
            logger.error("خطأ في حفظ الإعدادات - Error saving settings", e);
            return false;
        }
    }

    // Alert Helper Methods

    /**
     * عرض تنبيه خطأ
     * Show error alert
     */
    private void showErrorAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * عرض تنبيه معلومات
     * Show info alert
     */
    private void showInfoAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }

    /**
     * عرض تنبيه تحذير
     * Show warning alert
     */
    private void showWarningAlert(String title, String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        });
    }
}
