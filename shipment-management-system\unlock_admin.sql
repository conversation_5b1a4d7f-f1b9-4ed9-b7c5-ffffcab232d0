-- إنشاء أو تحديث المستخدم admin
-- Create or update admin user

-- حذف المستخدم إذا كان موجوداً
-- Delete user if exists
DELETE FROM users WHERE username = 'admin';

-- إدراج المستخدم الافتراضي
-- Insert default user
INSERT INTO users (
    user_id,
    username,
    password_hash,
    full_name,
    email,
    role,
    is_active,
    is_locked,
    failed_login_attempts,
    created_date,
    last_login,
    last_login_attempt
) VALUES (
    1,
    'admin',
    'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- admin123 hashed with SHA-256
    'مدير النظام',
    '<EMAIL>',
    'ADMIN',
    1,
    0,
    0,
    SYSDATE,
    NULL,
    NULL
);

COMMIT;

-- التحقق من المستخدم المُدرج
-- Check inserted user
SELECT
    user_id,
    username,
    full_name,
    email,
    role,
    is_active,
    is_locked,
    failed_login_attempts,
    created_date
FROM users
WHERE username = 'admin';

EXIT;
