-- إصلاح جدول المستخدمين
-- Fix users table

-- إضافة الأعمدة المفقودة إذا لم تكن موجودة
-- Add missing columns if they don't exist

-- التحقق من الأعمدة الموجودة
-- Check existing columns
SELECT column_name FROM user_tab_columns WHERE table_name = 'USERS' ORDER BY column_name;

-- إضافة الأعمدة المفقودة
-- Add missing columns
BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE users ADD (is_locked NUMBER(1) DEFAULT 0)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- Column already exists
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE users ADD (failed_login_attempts NUMBER(3) DEFAULT 0)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- Column already exists
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE users ADD (locked_until DATE)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- Column already exists
            RAISE;
        END IF;
END;
/

BEGIN
    EXECUTE IMMEDIATE 'ALTER TABLE users ADD (last_login_attempt DATE)';
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE != -1430 THEN -- Column already exists
            RAISE;
        END IF;
END;
/

-- حذف المستخدم admin إذا كان موجوداً
-- Delete admin user if exists
DELETE FROM users WHERE username = 'admin';

-- إدراج المستخدم الافتراضي
-- Insert default user
INSERT INTO users (
    username,
    password_hash,
    full_name,
    email,
    role,
    is_active,
    is_locked,
    failed_login_attempts,
    created_date
) VALUES (
    'admin',
    'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', -- admin123 hashed with SHA-256
    'مدير النظام',
    '<EMAIL>',
    'ADMIN',
    1,
    0,
    0,
    SYSDATE
);

COMMIT;

-- التحقق من المستخدم المُدرج
-- Check inserted user
SELECT 
    username,
    full_name,
    email,
    role,
    is_active,
    is_locked,
    failed_login_attempts,
    created_date
FROM users 
WHERE username = 'admin';

-- عرض هيكل الجدول
-- Show table structure
DESC users;

EXIT;
